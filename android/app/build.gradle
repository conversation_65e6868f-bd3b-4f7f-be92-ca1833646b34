def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new Exception("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    throw new GradleException("versionCode not found. Define flutter.versionCode in the local.properties file.")
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    throw new GradleException("versionName not found. Define flutter.versionName in the local.properties file.")
}

def keyStoreProperties = new Properties()
def keyStorePropertiesFile = rootProject.file('key.properties')
if (keyStorePropertiesFile.exists()) {
    keyStoreProperties.load(new FileInputStream(keyStorePropertiesFile))
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

android {
    namespace = "com.DDEK.etahlil"
    compileSdk = 35
    ndkVersion = "22.0.7026061"

    dexOptions {
        javaMaxHeapSize "4g"
    }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.DDEK.etahlil"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keyStoreProperties['keyAlias']
            keyPassword keyStoreProperties['keyPassword']
            storeFile keyStoreProperties['storeFile'] ? file(keyStoreProperties['storeFile']) : null
            storePassword keyStoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86_64"
            }
        }
        debug {
            signingConfig signingConfigs.debug
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86_64"
            }
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    def kotlin_version = '2.1.10'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.multidex:multidex:2.0.1"

    // Firebase
    implementation platform('com.google.firebase:firebase-bom:31.0.1')
    implementation 'com.google.firebase:firebase-analytics'
}