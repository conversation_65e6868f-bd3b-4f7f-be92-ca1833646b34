-- Merging decision tree log ---
application
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:35:5-61:19
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5948a48de9f64fe5e58dcd144c25b68b\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5948a48de9f64fe5e58dcd144c25b68b\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7636bf353b1110714e6cad062f827fbb\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7636bf353b1110714e6cad062f827fbb\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c999a474110f2e795ead18f2a6bea164\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c999a474110f2e795ead18f2a6bea164\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4cf7c50c02d755e4b4c65f2136b20cf0\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4cf7c50c02d755e4b4c65f2136b20cf0\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\f5e63a0c8d2d4cec6ad591edbb1b6248\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\f5e63a0c8d2d4cec6ad591edbb1b6248\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\54d23974072ac0a1eba6f759a4b8c94f\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\54d23974072ac0a1eba6f759a4b8c94f\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ca51c5c10a59534ef012fb93f3f77f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ca51c5c10a59534ef012fb93f3f77f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a546a734050dc3ae89ad6f78d2207432\transformed\core-1.2.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a546a734050dc3ae89ad6f78d2207432\transformed\core-1.2.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f2b2437644be9a773dd495b30364239\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f2b2437644be9a773dd495b30364239\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a546a734050dc3ae89ad6f78d2207432\transformed\core-1.2.0\AndroidManifest.xml:24:18-86
	android:name
		INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:1:1-62:12
MERGED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:1:1-62:12
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\4038668e1890dbf733cdddedbf6203f3\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5948a48de9f64fe5e58dcd144c25b68b\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:17:1-37:12
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7636bf353b1110714e6cad062f827fbb\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c999a474110f2e795ead18f2a6bea164\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4cf7c50c02d755e4b4c65f2136b20cf0\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\f5e63a0c8d2d4cec6ad591edbb1b6248\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71bbb456e82941b370a47d8ab0e193eb\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\54d23974072ac0a1eba6f759a4b8c94f\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ca51c5c10a59534ef012fb93f3f77f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\53f85b68faa285f715061fe3d5f7dfa0\transformed\fragment-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\819faa8ab0cef371610070e7d22be275\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\268673407f07b3f1e4354735298c9ece\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5a6433b76b72399fdc9125d6b0b850d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\46af8266122fa00eec5c92ea4bbba6cc\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab9e6cf0e1e76f7e19a9f4f820df25d0\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3864a29531d3d1551c37117532cf60a4\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b786eb378b4f28bbd1afcf6a546ad68a\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5c8250a50d2ad2424681eaab5508a50\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a80a30876bc2222ef0627fbfabe28726\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ee7854b241e558e7ba578874e4e3097\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a546a734050dc3ae89ad6f78d2207432\transformed\core-1.2.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f2b2437644be9a773dd495b30364239\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\e5976c50ce6fe52b499915c98ef11cee\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ebe4e4e45fd11712275b9d5ff7c1dd3b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1166ed4a2c95e0464ff48a00a3dd69e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d77ea91c100af2e03d43db283a01cc\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98fc3000fffa5c21a44b46365ad089fc\transformed\lifecycle-viewmodel-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ef643c55fb4a6a89aaee099924dcc0e\transformed\lifecycle-runtime-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed90756f07fb3b46933b24727c64d6be\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fbdd01ac4e474e4cf1c08fbb50fe0db7\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8846336666714b0847f2f31c37265ead\transformed\core-runtime-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f074355e4a897260fbe509e304570f05\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\087f660d44674d27f1ea0b9afa2b6592\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:3:5-31
		INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:2:5-51
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:5:5-65
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:5:22-62
uses-permission#android.permission.INTERNET
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:6:5-67
MERGED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:6:5-67
MERGED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:10:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.USE_FINGERPRINT
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:9:5-74
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:9:22-71
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:11:5-71
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:11:22-68
uses-feature#android.hardware.camera
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:13:5-15:35
	android:required
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:15:9-32
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:14:9-47
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:17:5-76
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:17:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:18:5-20:38
	android:maxSdkVersion
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:20:9-35
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:19:9-64
uses-permission#android.permission.READ_PHONE_STATE
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:22:5-24:31
	tools:node
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:24:9-28
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:23:9-59
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:25:5-27:31
	tools:node
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:27:9-28
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:26:9-61
uses-permission#android.permission.READ_SMS
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:28:5-30:31
	tools:node
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:30:9-28
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:29:9-51
uses-permission#android.permission.RECEIVE_SMS
ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:31:5-33:31
	tools:node
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:33:9-28
	android:name
		ADDED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:32:9-54
uses-sdk
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\4038668e1890dbf733cdddedbf6203f3\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\4038668e1890dbf733cdddedbf6203f3\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5948a48de9f64fe5e58dcd144c25b68b\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5948a48de9f64fe5e58dcd144c25b68b\transformed\jetified-firebase-analytics-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7636bf353b1110714e6cad062f827fbb\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7636bf353b1110714e6cad062f827fbb\transformed\jetified-play-services-measurement-sdk-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c999a474110f2e795ead18f2a6bea164\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c999a474110f2e795ead18f2a6bea164\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4cf7c50c02d755e4b4c65f2136b20cf0\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4cf7c50c02d755e4b4c65f2136b20cf0\transformed\jetified-play-services-measurement-base-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\f5e63a0c8d2d4cec6ad591edbb1b6248\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\f5e63a0c8d2d4cec6ad591edbb1b6248\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71bbb456e82941b370a47d8ab0e193eb\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-installations-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71bbb456e82941b370a47d8ab0e193eb\transformed\jetified-firebase-installations-interop-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\54d23974072ac0a1eba6f759a4b8c94f\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\54d23974072ac0a1eba6f759a4b8c94f\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ca51c5c10a59534ef012fb93f3f77f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\12ca51c5c10a59534ef012fb93f3f77f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\53f85b68faa285f715061fe3d5f7dfa0\transformed\fragment-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\53f85b68faa285f715061fe3d5f7dfa0\transformed\fragment-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\819faa8ab0cef371610070e7d22be275\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\819faa8ab0cef371610070e7d22be275\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\268673407f07b3f1e4354735298c9ece\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\268673407f07b3f1e4354735298c9ece\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5a6433b76b72399fdc9125d6b0b850d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5a6433b76b72399fdc9125d6b0b850d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\46af8266122fa00eec5c92ea4bbba6cc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\46af8266122fa00eec5c92ea4bbba6cc\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab9e6cf0e1e76f7e19a9f4f820df25d0\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab9e6cf0e1e76f7e19a9f4f820df25d0\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3864a29531d3d1551c37117532cf60a4\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3864a29531d3d1551c37117532cf60a4\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b786eb378b4f28bbd1afcf6a546ad68a\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b786eb378b4f28bbd1afcf6a546ad68a\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5c8250a50d2ad2424681eaab5508a50\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5c8250a50d2ad2424681eaab5508a50\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a80a30876bc2222ef0627fbfabe28726\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a80a30876bc2222ef0627fbfabe28726\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ee7854b241e558e7ba578874e4e3097\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ee7854b241e558e7ba578874e4e3097\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a546a734050dc3ae89ad6f78d2207432\transformed\core-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a546a734050dc3ae89ad6f78d2207432\transformed\core-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f2b2437644be9a773dd495b30364239\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f2b2437644be9a773dd495b30364239\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\e5976c50ce6fe52b499915c98ef11cee\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\e5976c50ce6fe52b499915c98ef11cee\transformed\jetified-firebase-components-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ebe4e4e45fd11712275b9d5ff7c1dd3b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ebe4e4e45fd11712275b9d5ff7c1dd3b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1166ed4a2c95e0464ff48a00a3dd69e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1166ed4a2c95e0464ff48a00a3dd69e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d77ea91c100af2e03d43db283a01cc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d77ea91c100af2e03d43db283a01cc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98fc3000fffa5c21a44b46365ad089fc\transformed\lifecycle-viewmodel-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98fc3000fffa5c21a44b46365ad089fc\transformed\lifecycle-viewmodel-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ef643c55fb4a6a89aaee099924dcc0e\transformed\lifecycle-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ef643c55fb4a6a89aaee099924dcc0e\transformed\lifecycle-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed90756f07fb3b46933b24727c64d6be\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed90756f07fb3b46933b24727c64d6be\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fbdd01ac4e474e4cf1c08fbb50fe0db7\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fbdd01ac4e474e4cf1c08fbb50fe0db7\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8846336666714b0847f2f31c37265ead\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8846336666714b0847f2f31c37265ead\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f074355e4a897260fbe509e304570f05\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f074355e4a897260fbe509e304570f05\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\087f660d44674d27f1ea0b9afa2b6592\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\087f660d44674d27f1ea0b9afa2b6592\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c999a474110f2e795ead18f2a6bea164\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c999a474110f2e795ead18f2a6bea164\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2efc94b5374d68e2ab1041f0d256bc\transformed\jetified-play-services-measurement-sdk-api-21.2.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:32:9-36:35
MERGED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:32:9-36:35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:30:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:36:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:29:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:18:17-127
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:27:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:29:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:28:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:26:13-77
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\caa458cc276ae74a94149ffb9b20eabf\transformed\jetified-play-services-measurement-impl-21.2.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
