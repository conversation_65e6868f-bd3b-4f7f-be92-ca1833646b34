<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="default_web_client_id" translatable="false">685267274560-iuqo7ibhlk8dj8nj2p90hepq4as7945e.apps.googleusercontent.com</string>
    <string name="gcm_defaultSenderId" translatable="false">685267274560</string>
    <string name="google_api_key" translatable="false">AIzaSyD4RZygTqDpkMMy2FFNo4JVpzltxBHdxpQ</string>
    <string name="google_app_id" translatable="false">1:685267274560:android:5fac6472079c0c104388a5</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyD4RZygTqDpkMMy2FFNo4JVpzltxBHdxpQ</string>
    <string name="google_storage_bucket" translatable="false">e-tahlil-app.appspot.com</string>
    <string name="project_id" translatable="false">e-tahlil-app</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>