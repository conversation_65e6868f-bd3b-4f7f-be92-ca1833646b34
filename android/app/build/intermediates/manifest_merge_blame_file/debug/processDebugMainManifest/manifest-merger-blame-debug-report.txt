1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.DDEK.etahlil"
4    android:versionCode="28"
5    android:versionName="3.1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:6:5-67
14-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:6:22-64
15    <!-- Add this line to explicitly set the package -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:5:5-65
16-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:5:22-62
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:7:5-79
17-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:7:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:8:5-81
18-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:8:22-78
19    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
19-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:9:5-74
19-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:9:22-71
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:10:5-81
20-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:10:22-78
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:11:5-71
21-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:11:22-68
22
23    <uses-feature
23-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:13:5-15:35
24        android:name="android.hardware.camera"
24-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:14:9-47
25        android:required="true" />
25-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:15:9-32
26
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:17:5-76
27-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:17:22-73
28    <uses-permission
28-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:18:5-20:38
29        android:name="android.permission.READ_EXTERNAL_STORAGE"
29-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:19:9-64
30        android:maxSdkVersion="32" />
30-->D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\AndroidManifest.xml:20:9-35
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:5-79
31-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:23:22-76
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:5-68
32-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:24:22-65
33    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
33-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
33-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
34    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
34-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
34-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
35
36    <application
37        android:name="android.app.Application"
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a546a734050dc3ae89ad6f78d2207432\transformed\core-1.2.0\AndroidManifest.xml:24:18-86
39        android:debuggable="true"
40        android:extractNativeLibs="true"
41        android:icon="@mipmap/launcher_icon"
42        android:label="E-tahlil"
43        android:requestLegacyExternalStorage="true" >
44        <activity
45            android:name="com.DDEK.etahlil.MainActivity"
46            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
47            android:exported="true"
48            android:hardwareAccelerated="true"
49            android:launchMode="singleTask"
50            android:theme="@style/LaunchTheme"
51            android:windowSoftInputMode="adjustResize" >
52            <meta-data
53                android:name="io.flutter.embedding.android.NormalTheme"
54                android:resource="@style/NormalTheme" />
55
56            <intent-filter>
57                <action android:name="android.intent.action.MAIN" />
58
59                <category android:name="android.intent.category.LAUNCHER" />
60            </intent-filter>
61        </activity>
62
63        <meta-data
64            android:name="flutterEmbedding"
65            android:value="2" />
66
67        <service
67-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:28:9-34:19
68            android:name="com.google.firebase.components.ComponentDiscoveryService"
68-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:29:13-84
69            android:directBootAware="true"
69-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:34:13-43
70            android:exported="false" >
70-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:30:13-37
71            <meta-data
71-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
72                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
72-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
73                android:value="com.google.firebase.components.ComponentRegistrar" />
73-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3497fc6a9f42c0d8066e21d078737e0\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
74            <meta-data
74-->[com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:17:13-19:85
75                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
75-->[com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:18:17-127
76                android:value="com.google.firebase.components.ComponentRegistrar" />
76-->[com.google.firebase:firebase-installations:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64abb23b7be4118f3b0e6055c86f4543\transformed\jetified-firebase-installations-17.1.0\AndroidManifest.xml:19:17-82
77        </service>
78
79        <provider
79-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:25:9-30:39
80            android:name="com.google.firebase.provider.FirebaseInitProvider"
80-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:26:13-77
81            android:authorities="com.DDEK.etahlil.firebaseinitprovider"
81-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:27:13-72
82            android:directBootAware="true"
82-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:28:13-43
83            android:exported="false"
83-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:29:13-37
84            android:initOrder="100" />
84-->[com.google.firebase:firebase-common:20.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\40ebc0e390678fdbefdf566f1d9da82f\transformed\jetified-firebase-common-20.2.0\AndroidManifest.xml:30:13-36
85
86        <receiver
86-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
87            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
87-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
88            android:enabled="true"
88-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
89            android:exported="false" >
89-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
90        </receiver>
91
92        <service
92-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
93            android:name="com.google.android.gms.measurement.AppMeasurementService"
93-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
94            android:enabled="true"
94-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
95            android:exported="false" />
95-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
96        <service
96-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
97            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
97-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
98            android:enabled="true"
98-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
99            android:exported="false"
99-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
100            android:permission="android.permission.BIND_JOB_SERVICE" />
100-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce6d5b25001c1fbbcdf3f71f1fe91bfc\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
101
102        <meta-data
102-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
103            android:name="com.google.android.gms.version"
103-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
104            android:value="@integer/google_play_services_version" />
104-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2f4441a27fd740f65ab86b90ae3b3d9\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
105    </application>
106
107</manifest>
