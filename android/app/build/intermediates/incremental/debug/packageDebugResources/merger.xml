<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res"><file name="background" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable\background.png" qualifiers="" type="drawable"/><file name="launch_background" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="android12splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-hdpi\android12splash.png" qualifiers="hdpi-v4" type="drawable"/><file name="splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-hdpi\splash.png" qualifiers="hdpi-v4" type="drawable"/><file name="android12splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-mdpi\android12splash.png" qualifiers="mdpi-v4" type="drawable"/><file name="splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-mdpi\splash.png" qualifiers="mdpi-v4" type="drawable"/><file name="background" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-v21\background.png" qualifiers="v21" type="drawable"/><file name="launch_background" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="android12splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-xhdpi\android12splash.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-xhdpi\splash.png" qualifiers="xhdpi-v4" type="drawable"/><file name="android12splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-xxhdpi\android12splash.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-xxhdpi\splash.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="android12splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-xxxhdpi\android12splash.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="splash" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\drawable-xxxhdpi\splash.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-hdpi\launcher_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-mdpi\launcher_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-xhdpi\launcher_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-xxhdpi\launcher_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\mipmap-xxxhdpi\launcher_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\main\res\values-v31\styles.xml" qualifiers="v31"><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:windowSplashScreenBackground">#FFFFFF</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/android12splash</item>
        <item name="android:windowSplashScreenIconBackgroundColor">#FFFFFF</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\res\resValues\debug"/><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\res\google-services\debug"/><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\crashlytics\res\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\res\resValues\debug"/><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\res\google-services\debug"><file path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\res\google-services\debug\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">685267274560-iuqo7ibhlk8dj8nj2p90hepq4as7945e.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">685267274560</string><string name="google_api_key" translatable="false">AIzaSyD4RZygTqDpkMMy2FFNo4JVpzltxBHdxpQ</string><string name="google_app_id" translatable="false">1:685267274560:android:5fac6472079c0c104388a5</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyD4RZygTqDpkMMy2FFNo4JVpzltxBHdxpQ</string><string name="google_storage_bucket" translatable="false">e-tahlil-app.appspot.com</string><string name="project_id" translatable="false">e-tahlil-app</string></file></source><source path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\crashlytics\res\debug"><file path="D:\Android\AndroidStudioProjects\e-tahlil-mobile\android\app\build\generated\crashlytics\res\debug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res" generated-set="legacy_api_res$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>