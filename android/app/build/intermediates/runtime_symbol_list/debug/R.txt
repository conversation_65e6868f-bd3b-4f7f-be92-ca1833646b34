int attr alpha 0x7f010000
int attr coordinatorLayoutStyle 0x7f010001
int attr font 0x7f010002
int attr fontProviderAuthority 0x7f010003
int attr fontProviderCerts 0x7f010004
int attr fontProviderFetchStrategy 0x7f010005
int attr fontProviderFetchTimeout 0x7f010006
int attr fontProviderPackage 0x7f010007
int attr fontProviderQuery 0x7f010008
int attr fontStyle 0x7f010009
int attr fontVariationSettings 0x7f01000a
int attr fontWeight 0x7f01000b
int attr keylines 0x7f01000c
int attr layout_anchor 0x7f01000d
int attr layout_anchorGravity 0x7f01000e
int attr layout_behavior 0x7f01000f
int attr layout_dodgeInsetEdges 0x7f010010
int attr layout_insetEdge 0x7f010011
int attr layout_keyline 0x7f010012
int attr statusBarBackground 0x7f010013
int attr ttcIndex 0x7f010014
int color notification_action_color_filter 0x7f020000
int color notification_icon_bg_color 0x7f020001
int color ripple_material_light 0x7f020002
int color secondary_text_default_material_light 0x7f020003
int dimen compat_button_inset_horizontal_material 0x7f030000
int dimen compat_button_inset_vertical_material 0x7f030001
int dimen compat_button_padding_horizontal_material 0x7f030002
int dimen compat_button_padding_vertical_material 0x7f030003
int dimen compat_control_corner_material 0x7f030004
int dimen compat_notification_large_icon_max_height 0x7f030005
int dimen compat_notification_large_icon_max_width 0x7f030006
int dimen notification_action_icon_size 0x7f030007
int dimen notification_action_text_size 0x7f030008
int dimen notification_big_circle_margin 0x7f030009
int dimen notification_content_margin_start 0x7f03000a
int dimen notification_large_icon_height 0x7f03000b
int dimen notification_large_icon_width 0x7f03000c
int dimen notification_main_column_padding_top 0x7f03000d
int dimen notification_media_narrow_margin 0x7f03000e
int dimen notification_right_icon_size 0x7f03000f
int dimen notification_right_side_padding_top 0x7f030010
int dimen notification_small_icon_background_padding 0x7f030011
int dimen notification_small_icon_size_as_large 0x7f030012
int dimen notification_subtext_size 0x7f030013
int dimen notification_top_pad 0x7f030014
int dimen notification_top_pad_large_text 0x7f030015
int drawable android12splash 0x7f040000
int drawable background 0x7f040001
int drawable launch_background 0x7f040002
int drawable notification_action_background 0x7f040003
int drawable notification_bg 0x7f040004
int drawable notification_bg_low 0x7f040005
int drawable notification_bg_low_normal 0x7f040006
int drawable notification_bg_low_pressed 0x7f040007
int drawable notification_bg_normal 0x7f040008
int drawable notification_bg_normal_pressed 0x7f040009
int drawable notification_icon_background 0x7f04000a
int drawable notification_template_icon_bg 0x7f04000b
int drawable notification_template_icon_low_bg 0x7f04000c
int drawable notification_tile_bg 0x7f04000d
int drawable notify_panel_notification_icon_bg 0x7f04000e
int drawable splash 0x7f04000f
int id accessibility_action_clickable_span 0x7f050000
int id accessibility_custom_action_0 0x7f050001
int id accessibility_custom_action_1 0x7f050002
int id accessibility_custom_action_10 0x7f050003
int id accessibility_custom_action_11 0x7f050004
int id accessibility_custom_action_12 0x7f050005
int id accessibility_custom_action_13 0x7f050006
int id accessibility_custom_action_14 0x7f050007
int id accessibility_custom_action_15 0x7f050008
int id accessibility_custom_action_16 0x7f050009
int id accessibility_custom_action_17 0x7f05000a
int id accessibility_custom_action_18 0x7f05000b
int id accessibility_custom_action_19 0x7f05000c
int id accessibility_custom_action_2 0x7f05000d
int id accessibility_custom_action_20 0x7f05000e
int id accessibility_custom_action_21 0x7f05000f
int id accessibility_custom_action_22 0x7f050010
int id accessibility_custom_action_23 0x7f050011
int id accessibility_custom_action_24 0x7f050012
int id accessibility_custom_action_25 0x7f050013
int id accessibility_custom_action_26 0x7f050014
int id accessibility_custom_action_27 0x7f050015
int id accessibility_custom_action_28 0x7f050016
int id accessibility_custom_action_29 0x7f050017
int id accessibility_custom_action_3 0x7f050018
int id accessibility_custom_action_30 0x7f050019
int id accessibility_custom_action_31 0x7f05001a
int id accessibility_custom_action_4 0x7f05001b
int id accessibility_custom_action_5 0x7f05001c
int id accessibility_custom_action_6 0x7f05001d
int id accessibility_custom_action_7 0x7f05001e
int id accessibility_custom_action_8 0x7f05001f
int id accessibility_custom_action_9 0x7f050020
int id action_container 0x7f050021
int id action_divider 0x7f050022
int id action_image 0x7f050023
int id action_text 0x7f050024
int id actions 0x7f050025
int id all 0x7f050026
int id async 0x7f050027
int id blocking 0x7f050028
int id bottom 0x7f050029
int id center 0x7f05002a
int id center_horizontal 0x7f05002b
int id center_vertical 0x7f05002c
int id chronometer 0x7f05002d
int id clip_horizontal 0x7f05002e
int id clip_vertical 0x7f05002f
int id dialog_button 0x7f050030
int id end 0x7f050031
int id fill 0x7f050032
int id fill_horizontal 0x7f050033
int id fill_vertical 0x7f050034
int id forever 0x7f050035
int id icon 0x7f050036
int id icon_group 0x7f050037
int id info 0x7f050038
int id italic 0x7f050039
int id left 0x7f05003a
int id line1 0x7f05003b
int id line3 0x7f05003c
int id none 0x7f05003d
int id normal 0x7f05003e
int id notification_background 0x7f05003f
int id notification_main_column 0x7f050040
int id notification_main_column_container 0x7f050041
int id right 0x7f050042
int id right_icon 0x7f050043
int id right_side 0x7f050044
int id start 0x7f050045
int id tag_accessibility_actions 0x7f050046
int id tag_accessibility_clickable_spans 0x7f050047
int id tag_accessibility_heading 0x7f050048
int id tag_accessibility_pane_title 0x7f050049
int id tag_screen_reader_focusable 0x7f05004a
int id tag_transition_group 0x7f05004b
int id tag_unhandled_key_event_manager 0x7f05004c
int id tag_unhandled_key_listeners 0x7f05004d
int id text 0x7f05004e
int id text2 0x7f05004f
int id time 0x7f050050
int id title 0x7f050051
int id top 0x7f050052
int integer google_play_services_version 0x7f060000
int integer status_bar_notification_info_maxnum 0x7f060001
int layout custom_dialog 0x7f070000
int layout notification_action 0x7f070001
int layout notification_action_tombstone 0x7f070002
int layout notification_template_custom_big 0x7f070003
int layout notification_template_icon_group 0x7f070004
int layout notification_template_part_chronometer 0x7f070005
int layout notification_template_part_time 0x7f070006
int mipmap ic_launcher 0x7f080000
int mipmap launcher_icon 0x7f080001
int raw firebase_common_keep 0x7f090000
int string com_google_firebase_crashlytics_mapping_file_id 0x7f0a0000
int string common_google_play_services_unknown_issue 0x7f0a0001
int string default_web_client_id 0x7f0a0002
int string gcm_defaultSenderId 0x7f0a0003
int string google_api_key 0x7f0a0004
int string google_app_id 0x7f0a0005
int string google_crash_reporting_api_key 0x7f0a0006
int string google_storage_bucket 0x7f0a0007
int string project_id 0x7f0a0008
int string status_bar_notification_info_overflow 0x7f0a0009
int style LaunchTheme 0x7f0b0000
int style NormalTheme 0x7f0b0001
int style TextAppearance_Compat_Notification 0x7f0b0002
int style TextAppearance_Compat_Notification_Info 0x7f0b0003
int style TextAppearance_Compat_Notification_Line2 0x7f0b0004
int style TextAppearance_Compat_Notification_Time 0x7f0b0005
int style TextAppearance_Compat_Notification_Title 0x7f0b0006
int style Widget_Compat_NotificationActionContainer 0x7f0b0007
int style Widget_Compat_NotificationActionText 0x7f0b0008
int style Widget_Support_CoordinatorLayout 0x7f0b0009
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x7f010000 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_alpha 2
int[] styleable CoordinatorLayout { 0x7f01000c, 0x7f010013 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f01000d, 0x7f01000e, 0x7f01000f, 0x7f010010, 0x7f010011, 0x7f010012 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable FontFamily { 0x7f010003, 0x7f010004, 0x7f010005, 0x7f010006, 0x7f010007, 0x7f010008 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f010002, 0x7f010009, 0x7f01000a, 0x7f01000b, 0x7f010014 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
