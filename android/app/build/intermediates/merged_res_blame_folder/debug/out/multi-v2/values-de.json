{"logs": [{"outputFile": "com.DDEK.etahlil.app-mergeDebugResources-8:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929c8a20ea7e1c5b23a42b9543f487d7\\transformed\\core-1.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "200", "endColumns": "100", "endOffsets": "296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\438077e4ee9d40168521af9ad2e6fbf5\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "144", "endOffsets": "195"}}]}]}