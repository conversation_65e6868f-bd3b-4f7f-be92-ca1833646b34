{"logs": [{"outputFile": "com.DDEK.etahlil.app-mergeDebugResources-8:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\929c8a20ea7e1c5b23a42b9543f487d7\\transformed\\core-1.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "163", "endColumns": "100", "endOffsets": "259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\438077e4ee9d40168521af9ad2e6fbf5\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "107", "endOffsets": "158"}}]}]}