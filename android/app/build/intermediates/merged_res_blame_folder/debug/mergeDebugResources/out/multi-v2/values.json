{"logs": [{"outputFile": "com.DDEK.etahlil.app-mergeDebugResources-8:/values/values.xml", "map": [{"source": "D:\\Android\\AndroidStudioProjects\\e-tahlil-mobile\\android\\app\\build\\generated\\res\\google-services\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,710", "endColumns": "143,81,103,108,119,95,72", "endOffsets": "194,276,380,489,609,705,778"}, "to": {"startLines": "81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5175,5319,5401,5505,5614,5734,5830", "endColumns": "143,81,103,108,119,95,72", "endOffsets": "5314,5396,5500,5609,5729,5825,5898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ab9e6cf0e1e76f7e19a9f4f820df25d0\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "2,109,119,125", "startColumns": "4,4,4,4", "startOffsets": "150,7221,7577,7788", "endLines": "2,111,124,208", "endColumns": "60,12,24,24", "endOffsets": "206,7361,7783,12299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a546a734050dc3ae89ad6f78d2207432\\transformed\\core-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,83,84,88,89,90,91,98,141,173,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,245,319,389,457,529,599,660,734,807,868,929,991,1055,1117,1178,1246,1346,1406,1472,1545,1614,1671,1723,1785,1857,1933,1998,2057,2116,2176,2236,2296,2356,2416,2476,2536,2596,2656,2716,2775,2835,2895,2955,3015,3075,3135,3195,3255,3315,3375,3434,3494,3554,3613,3672,3731,3790,3849,3908,3943,3978,4033,4096,4151,4209,4266,4316,4377,4434,4468,4503,4538,4608,4679,4796,4997,5107,5308,5437,5509,5576,5874,8780,10845,12605", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,82,83,87,88,89,90,97,140,172,209,216", "endColumns": "68,62,57,73,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "119,182,240,314,384,452,524,594,655,729,802,863,924,986,1050,1112,1173,1241,1341,1401,1467,1540,1609,1666,1718,1780,1852,1928,1993,2052,2111,2171,2231,2291,2351,2411,2471,2531,2591,2651,2711,2770,2830,2890,2950,3010,3070,3130,3190,3250,3310,3370,3429,3489,3549,3608,3667,3726,3785,3844,3903,3938,3973,4028,4091,4146,4204,4261,4311,4372,4429,4463,4498,4533,4603,4674,4791,4992,5102,5303,5432,5504,5571,5869,8775,10840,12600,12977"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,78,88,96,97,101,102,106,107,108,112,209,240,261,294", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "211,280,343,401,475,545,613,685,755,816,890,963,1024,1085,1147,1211,1273,1334,1402,1502,1562,1628,1701,1770,1827,1879,1941,2013,2089,2154,2213,2272,2332,2392,2452,2512,2572,2632,2692,2752,2812,2872,2931,2991,3051,3111,3171,3231,3291,3351,3411,3471,3531,3590,3650,3710,3769,3828,3887,3946,4005,4064,4099,4134,4189,4252,4307,4365,4422,4472,4533,4590,4624,4659,4762,5903,6324,6441,6642,6752,6953,7082,7154,7366,12304,13958,14639,15321", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,78,88,96,100,101,105,106,107,108,118,239,260,293,299", "endColumns": "68,62,57,73,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "275,338,396,470,540,608,680,750,811,885,958,1019,1080,1142,1206,1268,1329,1397,1497,1557,1623,1696,1765,1822,1874,1936,2008,2084,2149,2208,2267,2327,2387,2447,2507,2567,2627,2687,2747,2807,2867,2926,2986,3046,3106,3166,3226,3286,3346,3406,3466,3526,3585,3645,3705,3764,3823,3882,3941,4000,4059,4094,4129,4184,4247,4302,4360,4417,4467,4528,4585,4619,4654,4689,4827,5969,6436,6637,6747,6948,7077,7149,7216,7572,13953,14634,15316,15483"}}, {"source": "D:\\Android\\AndroidStudioProjects\\e-tahlil-mobile\\android\\app\\build\\generated\\crashlytics\\res\\debug\\values\\com_google_firebase_crashlytics_mappingfileid.xml", "from": {"startLines": "8", "startColumns": "0", "startOffsets": "286", "endColumns": "173", "endOffsets": "459"}, "to": {"startLines": "79", "startColumns": "4", "startOffsets": "4832", "endColumns": "175", "endOffsets": "5003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c2f4441a27fd740f65ab86b90ae3b3d9\\transformed\\jetified-play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "77,80", "startColumns": "4,4", "startOffsets": "4694,5008", "endColumns": "67,166", "endOffsets": "4757,5170"}}, {"source": "D:\\Android\\AndroidStudioProjects\\e-tahlil-mobile\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "89,93", "startColumns": "4,4", "startOffsets": "5974,6155", "endLines": "92,95", "endColumns": "12,12", "endOffsets": "6150,6319"}}]}]}