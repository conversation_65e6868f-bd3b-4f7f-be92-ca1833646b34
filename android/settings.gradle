pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        def localProperties = new File(rootProject.projectDir, "local.properties")
        if (localProperties.exists()) {
            localProperties.withInputStream { properties.load(it) }
        }
        def flutterSdk = properties.getProperty("flutter.sdk")
        assert flutterSdk != null, "flutter.sdk not set in local.properties"
        return flutterSdk
    }

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    plugins {
        id "dev.flutter.flutter-plugin-loader" version "1.0.0"
        id "com.android.application" version "7.3.0" apply false
        id "org.jetbrains.kotlin.android" version "2.1.10" apply false
    }

    includeBuild("${flutterSdkPath()}/packages/flutter_tools/gradle")
}

include ':app'

def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
def properties = new Properties()

assert localPropertiesFile.exists()
localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }

def flutterSdkPath = properties.getProperty("flutter.sdk")
assert flutterSdkPath != null, "flutter.sdk not set in local.properties"

//gradle.beforeProject({ project ->
//    project.setProperty("target-platform", "android-arm,android-arm64")
//})

apply from: "$flutterSdkPath/packages/flutter_tools/gradle/app_plugin_loader.gradle"