<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>E-Tahlil</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>E-Tahlil</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsLocalNetworking</key>
			<true/>
		</dict>
		<key>NSCameraUsageDescription</key>
		<string>Granting access to the camera enables the app to capture photos directly within the application and attach them to the application form. This functionality allows users to conveniently include visual content within their submissions. The app does not store or utilize the captured images for any purpose other than immediate attachment within the application. The images are not stored or shared externally.</string>
		<key>NSFaceIDUsageDescription</key>
		<string>Can I use your face ID as option to unlock our app?</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>This permission allows the app to access the user's location in order to determine their current location and share this information within the application form. The location data is used exclusively within the context of the app's functionality to enhance the user experience and provide location-based features. The app does not utilize or disclose the location information for any other purpose or share it with external parties.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>This permission allows the app to access the user's location in order to determine their current location and share this information within the application form. The location data is used exclusively within the context of the app's functionality to enhance the user experience and provide location-based features. The app does not utilize or disclose the location information for any other purpose or share it with external parties.</string>
		<key>NSLocationUsageDescription</key>
		<string>This permission allows the app to access the user's location in order to determine their current location and share this information within the application form. The location data is used exclusively within the context of the app's functionality to enhance the user experience and provide location-based features. The app does not utilize or disclose the location information for any other purpose or share it with external parties.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This permission allows the app to access the user's location in order to determine their current location and share this information within the application form. The location data is used exclusively within the context of the app's functionality to enhance the user experience and provide location-based features. The app does not utilize or disclose the location information for any other purpose or share it with external parties.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>This permission enables the app to record audio within the application and attach it to the application form. Users can utilize this feature to include voice recordings as part of their submissions. The app exclusively uses the recorded audio within the application and does not employ it for any other purposes. The audio recordings are not shared externally or used for any unrelated activities.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This permission allows the app to access photos from the user's gallery and attach them to the application form. By granting access, users can conveniently select and include existing photos from their gallery within their submissions. The app solely utilizes the selected photos within the application for the purpose of attaching them to the application form. The photos are not stored or shared externally.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIFileSharingEnabled</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
	</dict>
</plist>
