PODS:
  - audio_session (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - device_info (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (9.5.0):
    - FirebaseCore (= 9.5.0)
  - Firebase/Crashlytics (9.5.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 9.5.0)
  - firebase_core (1.22.0):
    - Firebase/CoreOnly (= 9.5.0)
    - Flutter
  - firebase_crashlytics (2.8.10):
    - Firebase/Crashlytics (= 9.5.0)
    - firebase_core
    - Flutter
  - FirebaseCore (9.5.0):
    - FirebaseCoreDiagnostics (~> 9.0)
    - FirebaseCoreInternal (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (9.6.0):
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCoreInternal (9.6.0):
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
  - FirebaseCrashlytics (9.5.0):
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (9.6.0):
    - FirebaseCore (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (~> 2.1)
  - Flutter (1.0.0)
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_native_image (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - gallery_saver (0.0.1):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleDataTransport (9.2.0):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Environment (7.10.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.10.0):
    - GoogleUtilities/Environment
  - "GoogleUtilities/NSData+zlib (7.10.0)"
  - GoogleUtilities/UserDefaults (7.10.0):
    - GoogleUtilities/Logger
  - image_picker_ios (0.0.1):
    - Flutter
  - images_picker (0.0.1):
    - Flutter
    - ZLPhotoBrowser (= 4.2.5)
  - just_audio (0.0.1):
    - Flutter
  - libwebp (1.2.4):
    - libwebp/demux (= 1.2.4)
    - libwebp/mux (= 1.2.4)
    - libwebp/webp (= 1.2.4)
  - libwebp/demux (1.2.4):
    - libwebp/webp
  - libwebp/mux (1.2.4):
    - libwebp/demux
  - libwebp/webp (1.2.4)
  - local_auth (0.0.1):
    - Flutter
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - native_device_orientation (0.0.1):
    - Flutter
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_ios (0.0.1):
    - Flutter
  - permission_handler_apple (9.1.0):
    - Flutter
  - platform_device_id (0.0.1):
    - Flutter
  - PromisesObjC (2.1.1)
  - record (0.0.1):
    - Flutter
  - SDWebImage (5.14.3):
    - SDWebImage/Core (= 5.14.3)
  - SDWebImage/Core (5.14.3)
  - SDWebImageWebPCoder (0.9.1):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.13)
  - shared_preferences_ios (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toast (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - ZLPhotoBrowser (4.2.5):
    - ZLPhotoBrowser/Core (= 4.2.5)
  - ZLPhotoBrowser/Core (4.2.5)

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_native_image (from `.symlinks/plugins/flutter_native_image/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - gallery_saver (from `.symlinks/plugins/gallery_saver/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - images_picker (from `.symlinks/plugins/images_picker/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - local_auth (from `.symlinks/plugins/local_auth/ios`)
  - native_device_orientation (from `.symlinks/plugins/native_device_orientation/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - platform_device_id (from `.symlinks/plugins/platform_device_id/ios`)
  - record (from `.symlinks/plugins/record/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FMDB
    - GoogleDataTransport
    - GoogleUtilities
    - libwebp
    - Mantle
    - nanopb
    - OrderedSet
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - Toast
    - ZLPhotoBrowser

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_native_image:
    :path: ".symlinks/plugins/flutter_native_image/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  gallery_saver:
    :path: ".symlinks/plugins/gallery_saver/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  images_picker:
    :path: ".symlinks/plugins/images_picker/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  local_auth:
    :path: ".symlinks/plugins/local_auth/ios"
  native_device_orientation:
    :path: ".symlinks/plugins/native_device_orientation/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  platform_device_id:
    :path: ".symlinks/plugins/platform_device_id/ios"
  record:
    :path: ".symlinks/plugins/record/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  audio_session: 4f3e461722055d21515cf3261b64c973c062f345
  audioplayers_darwin: 387322cb364026a1782298c982693b1b6aa9fa1b
  camera_avfoundation: 07c77549ea54ad95d8581be86617c094a46280d9
  device_info: d7d233b645a32c40dfdc212de5cf646ca482f175
  device_info_plus: e5c5da33f982a436e103237c0c85f9031142abed
  Firebase: 800f16f07af493d98d017446a315c27af0552f41
  firebase_core: 31872a49ffe0bf6f834e7044c7334e433536735a
  firebase_crashlytics: 1d9af2a9b4c0a3d4ed3ee61c7b4d053484d1a476
  FirebaseCore: 25c0400b670fd1e2f2104349cd3b5dcce8d9418f
  FirebaseCoreDiagnostics: 99a495094b10a57eeb3ae8efa1665700ad0bdaa6
  FirebaseCoreInternal: bca76517fe1ed381e989f5e7d8abb0da8d85bed3
  FirebaseCrashlytics: d20fa38bb8c88cb0f1c9211286bc23ab58c1b464
  FirebaseInstallations: 0a115432c4e223c5ab20b0dbbe4cbefa793a0e8e
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_image_compress: 5a5e9aee05b6553048b8df1c3bc456d0afaac433
  flutter_inappwebview: bfd58618f49dc62f2676de690fc6dcda1d6c3721
  flutter_native_image: 9c0b7451838484458e5b0fae007b86a4c2d4bdfe
  flutter_native_splash: 52501b97d1c0a5f898d687f1646226c1f93c56ef
  fluttertoast: 16fbe6039d06a763f3533670197d01fc73459037
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  gallery_saver: 9fc173c9f4fcc48af53b2a9ebea1b643255be542
  geolocator_apple: cc556e6844d508c95df1e87e3ea6fa4e58c50401
  GoogleDataTransport: 1c8145da7117bd68bbbed00cf304edb6a24de00f
  GoogleUtilities: bad72cb363809015b1f7f19beb1f1cd23c589f95
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  images_picker: fa9364e3a7d3083c49f865fcfb2b9e7cdc574d3a
  just_audio: baa7252489dbcf47a4c7cc9ca663e9661c99aafa
  libwebp: f62cb61d0a484ba548448a4bd52aabf150ff6eef
  local_auth: 1740f55d7af0a2e2a8684ce225fe79d8931e808c
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  native_device_orientation: 3b4cfc9565a7b879cc4fde282b3e27745e852d0d
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: fd030dabf36271f146f1f3beacd48f564b0f17f7
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  permission_handler_apple: 8f116445eff3c0e7c65ad60f5fef5490aa94b4e4
  platform_device_id: 81b3e2993881f87d0c82ef151dc274df4869aef5
  PromisesObjC: ab77feca74fa2823e7af4249b8326368e61014cb
  record: 7ee2393532f8553bbb09fa19e95478323b7c0a99
  SDWebImage: 9c36e66c8ce4620b41a7407698dda44211a96764
  SDWebImageWebPCoder: 18503de6621dd2c420d680e33d46bf8e1d5169b0
  shared_preferences_ios: 548a61f8053b9b8a49ac19c1ffbc8b92c50d68ad
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  url_launcher_ios: 839c58cdb4279282219f5e248c3321761ff3c4de
  ZLPhotoBrowser: 4bfab86b851042e18d7f413284472aa68759626a

PODFILE CHECKSUM: 9130ae10df584d44249ff4186ec5d0a3fd43fe1f

COCOAPODS: 1.12.1
