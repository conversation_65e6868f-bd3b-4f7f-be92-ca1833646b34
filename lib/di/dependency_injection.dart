import 'dart:io';

import 'package:camera/camera.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:etahlil/core/functions.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:external_path/external_path.dart';
import 'package:http/http.dart' as http;
import 'package:etahlil/core/photo/image_picker_utils.dart';
import 'package:etahlil/core/location/location_service.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/features/auth/data/datasources/auth_local_datasources.dart';
import 'package:etahlil/features/auth/data/datasources/auth_remote_datasources.dart';
import 'package:etahlil/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:etahlil/features/auth/domain/repositories/auth_repository.dart';
import 'package:etahlil/features/auth/domain/usescases/auth.dart';
import 'package:etahlil/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:etahlil/features/home/<USER>/datasources/home_local_datasources.dart';
import 'package:etahlil/features/home/<USER>/datasources/home_remote_datasources.dart';
import 'package:etahlil/features/home/<USER>/models/category_model1.dart';
import 'package:etahlil/features/home/<USER>/models/sub_category_model.dart';
import 'package:etahlil/features/home/<USER>/repositories/repository_impl.dart';
import 'package:etahlil/features/home/<USER>/repositories/home_repository.dart';
import 'package:etahlil/features/home/<USER>/usescases/u_category.dart';
import 'package:etahlil/features/home/<USER>/usescases/u_sub_category.dart';
import 'package:etahlil/features/home/<USER>/bloc/category/category_bloc.dart';
import 'package:etahlil/features/home/<USER>/bloc/subCategory/sub_category_bloc.dart';
import 'package:etahlil/features/kutilmoqda/data/datasoursec/yuborilmagan_local_datasources.dart';
import 'package:etahlil/features/kutilmoqda/data/model/not_send_model1.dart';
import 'package:etahlil/features/kutilmoqda/data/repositories/yuborilmagan_repository_impl.dart';
import 'package:etahlil/features/kutilmoqda/domain/repository/yuborilmagan_repository.dart';
import 'package:etahlil/features/kutilmoqda/domain/usescases/u_not_send.dart';
import 'package:etahlil/features/kutilmoqda/domain/usescases/u_not_send_local.dart';
import 'package:etahlil/features/kutilmoqda/presentetion/bloc/not_send_bloc.dart';
import 'package:etahlil/features/lock/data/datasources/lock_local_datasources.dart';
import 'package:etahlil/features/lock/data/repositories/lock_repositories.dart';
import 'package:etahlil/features/lock/domain/repositories/lock_repositories.dart';
import 'package:etahlil/features/lock/domain/usescases/u_lock.dart';
import 'package:etahlil/features/login/data/datasources/login_local_datasources.dart';
import 'package:etahlil/features/login/data/datasources/login_remote_datasources.dart';
import 'package:etahlil/features/login/data/repositories/login_repository_impl.dart';
import 'package:etahlil/features/login/domain/repositories/login_repository.dart';
import 'package:etahlil/features/login/domain/usescases/u_login.dart';
import 'package:etahlil/features/login/presentation/bloc/login_bloc.dart';
import 'package:etahlil/features/new_history/data/datasoursec/new_history_local_datasources.dart';
import 'package:etahlil/features/new_history/data/datasoursec/new_history_remote_datasources.dart';
import 'package:etahlil/features/new_history/data/models/new_history_model.dart';
import 'package:etahlil/features/new_history/data/repositories/new_history_repository_impl.dart';
import 'package:etahlil/features/new_history/domain/repository/new_history_repository.dart';
import 'package:etahlil/features/new_history/domain/usescases/u_new_history.dart';
import 'package:etahlil/features/new_history/presentetion/bloc/new_history_bloc.dart';
import 'package:etahlil/features/old_history/data/datasoursec/old_history_local_datasources.dart';
import 'package:etahlil/features/old_history/data/datasoursec/old_history_remote_datasources.dart';
import 'package:etahlil/features/old_history/data/models/old_history_model.dart';
import 'package:etahlil/features/old_history/data/repositories/old_history_repository_impl.dart';
import 'package:etahlil/features/old_history/domain/repository/old_history_repository.dart';
import 'package:etahlil/features/old_history/domain/usescases/u_old_history.dart';
import 'package:etahlil/features/old_history/presentetion/bloc/old_history_bloc.dart';
import 'package:etahlil/features/profile/data/datasources/profile_local_datasources.dart';
import 'package:etahlil/features/profile/data/datasources/profile_remote_datasources.dart';
import 'package:etahlil/features/profile/data/models/prof_model.dart';
import 'package:etahlil/features/profile/data/repositories/profile_repository_impl.dart';
import 'package:etahlil/features/profile/domain/usescases/u_profile.dart';
import 'package:etahlil/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:etahlil/features/select_part/data/datasoursec/select_part_local_datasources.dart';
import 'package:etahlil/features/select_part/data/datasoursec/select_part_remote_datasources.dart';
import 'package:etahlil/features/select_part/data/repositories/select_part_repository_impl.dart';
import 'package:etahlil/features/select_part/domain/repository/select_part_repository.dart';
import 'package:etahlil/features/select_part/domain/usescases/u_select_part.dart';
import 'package:etahlil/features/select_part/domain/usescases/u_select_sub_part.dart';
import 'package:etahlil/features/select_part/presentetion/bloc/select_part_bloc.dart';
import 'package:etahlil/features/send_data/data/datasoursec/send_data_local_datasources.dart';
import 'package:etahlil/features/send_data/data/datasoursec/send_data_remote_datasources.dart';
import 'package:etahlil/features/send_data/data/models/img_model.dart';
import 'package:etahlil/features/send_data/data/repositories/send_data_repositoryimpl.dart';
import 'package:etahlil/features/send_data/domain/repository/send_data_repository.dart';
import 'package:etahlil/features/send_data/domain/usescase/u_send_data.dart';
import 'package:etahlil/features/send_data/presentetion/bloc/send_data_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../features/kutilmoqda/data/datasoursec/yuborilmagan_remote_datasources.dart';
import '../features/lock/presentation/bloc/pass_bloc.dart';
import '../features/password/presentation/bloc/pin_bloc.dart';

final di = GetIt.instance;
//di is referred to as Service Locator

Future<void> init() async {
  /// Local cache
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  di.registerLazySingleton(() => prefs);

  ///Initializing BASE_URL
  if (baseUrl == 'base') {
    var provinceId = prefs.getString('provinceId');
    var isBaseAvailable = prefs.getString(BASE_URL) != null ? true : false;
    if (provinceId != null && provinceId == ferganaId && !isBaseAvailable) {
      baseUrl = ferganaUrl;
      print('Old Fergana user logged in');
    } else {
      baseUrl = prefs.getString(BASE_URL) ?? baseUrl;
      print('Logged via URL selector');
    }
  }

  /// Hive (Local datasources)

  await Hive.initFlutter();

  ///Versioning
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  version = packageInfo.version;
  var helper = HiveHelper();

  bool isSameVer = (prefs.getString('ver') ?? '0') == version ? true : false;

  if (!isSameVer) {
    ///=============================================

    await prefs.remove('dateHome');
    await prefs.remove('dateNew');
    await prefs.remove('dateOld');
    await prefs.remove('dateProf');
    prefs.setString('ver', version);

    ///=============================================

    await helper.deleteBoxes();
  }

  ///Android version

  if (Platform.isAndroid) {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    final androidInfo = await deviceInfoPlugin.androidInfo;
    di.registerLazySingleton(() => androidInfo);
  }

  /// Blocs
  // sendData
  di.registerFactory(
    () => SendDataBloc(
      sendData: di(),
    ),
  );
  // password
  di.registerFactory(
    () => PinBloc(
      sharedPreferences: di(),
    ),
  );
  //lock
  di.registerFactory(
    () => PassBloc(
      pass: di(),
    ),
  );
  //home
  di.registerFactory(
    () => CategoryBloc(home: di()),
  );
  di.registerFactory(
    () => SubCategoryBloc(subCategory: di()),
  );
  //new history
  di.registerFactory(
    () => NewHistoryBloc(newHistory: di()),
  );
  //old history
  di.registerFactory(
    () => OldHistoryBloc(oldHistory: di()),
  );
  //login
  di.registerFactory(
    () => LoginBloc(loginData: di()),
  );
  //auth
  di.registerFactory(
    () => AuthBloc(authData: di()),
  );
  //profile
  di.registerFactory(
    () => ProfileBloc(profData: di()),
  );
  //selects
  di.registerFactory(
    () => SelectPartBloc(uSelectPart: di(), uSelectSubPart: di()),
  );
  //not send
  di.registerFactory(
    () => NotSendBloc(notSend: di(), notSendLocal: di()),
  );

  ///Repositories
  // sendData
  di.registerLazySingleton<SendDataRepository>(
    () => SendDataRepositoryImpl(
        networkInfo: di(),
        dataRemoteDatasource: di(),
        sharedPreferences: di(),
        dataLocalDatasource: di()),
  );
  // lock
  di.registerLazySingleton<PassRepository>(
    () => PassRepositoryImpl(passLocalDataSource: di()),
  );

  // home
  di.registerLazySingleton<HomeRepository>(
    () => HomeRepositoryImpl(
        networkInfo: di(),
        homeRemoteDatasourceImpl: di(),
        homeLocalDatasourceImpl: di()),
  );

  // new history
  di.registerLazySingleton<NewHistoryRepository>(
    () => NewHistoryRepositoryImpl(
        networkInfo: di(),
        newHistoryRemoteDatasourceImpl: di(),
        newHistoryLocalDatasourceImpl: di()),
  );
  // old history
  di.registerLazySingleton<OldHistoryRepository>(
    () => OldHistoryRepositoryImpl(
        networkInfo: di(),
        oldHistoryRemoteDatasourceImpl: di(),
        oldHistoryLocalDatasourceImpl: di()),
  );
  // login
  di.registerLazySingleton<LoginRepository>(
    () => LoginRepositoryImpl(
        networkInfo: di(),
        loginRemoteDatasource: di(),
        loginLocalDatasource: di()),
  );
  // auth
  di.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
        networkInfo: di(),
        authRemoteDatasource: di(),
        authLocalDatasource: di()),
  );
  // profile
  di.registerLazySingleton(
    () => ProfRepositoryImpl(
        networkInfo: di(),
        profRemoteDatasource: di(),
        profLocalDatasource: di()),
  );
  // selects
  di.registerLazySingleton<SelectPartRepository>(
    () => SelectPartRepositoryImpl(
      networkInfo: di(),
      selectPartRemoteDatasourceImpl: di(),
      selectPartLocalDatasourceImpl: di(),
    ),
  );
  // not send
  di.registerLazySingleton<NotSendRepository>(
    () => NotSendRepositoryImpl(
        notSendDataSourcesImpl: di(),
        notSendRemoteDataSourcesImpl: di(),
        networkInfo: di()),
  );

  /// UsesCases
  // sendData
  di.registerLazySingleton(() => SendData(sendDataRepository: di()));
  // lock
  di.registerLazySingleton(() => Pass(repository: di()));
  // home
  di.registerLazySingleton(() => UCategory(homeRepository: di()));
  di.registerLazySingleton(() => USubCategory(homeRepository: di()));
  //new history
  di.registerLazySingleton(() => UNewHistory(newHistoryRepo: di()));
  //old history
  di.registerLazySingleton(() => UOldHistory(oldHistoryRepo: di()));
  //login
  di.registerLazySingleton(() => LoginData(loginRepository: di()));
  //auth
  di.registerLazySingleton(() => AuthData(authRepository: di()));
  //profile
  di.registerLazySingleton(() => ProfData(profRepository: di()));
  //selects
  di.registerLazySingleton(() => USelectPart(selectPartRepo: di()));
  //selects
  di.registerLazySingleton(() => USelectSubPart(selectPartRepo: di()));
  //selects
  di.registerLazySingleton(() => NotSend(notSendRepository: di()));
  di.registerLazySingleton(() => NotSendLocal(notSendRepository: di()));

  /// Data sources
  //send data
  di.registerLazySingleton(
    () => SendDataRemoteDatasourceImpl(sharedPreferences: di(), client: di()),
  );
  di.registerLazySingleton(
    () => SendDataLocalDatasourceImpl(),
  );
  //lock
  di.registerLazySingleton(
    () => PassLocalDataSourceImpl(sharedPreferences: di()),
  );
  //new history
  di.registerLazySingleton(
    () => NewHistoryRemoteDatasourceImpl(sharedPreferences: di(), client: di()),
  );
  di.registerLazySingleton(
    () => NewHistoryDataSourcesImpl(),
  );
  //old history
  di.registerLazySingleton(
    () => OldHistoryRemoteDatasourceImpl(sharedPreferences: di(), client: di()),
  );
  di.registerLazySingleton(
    () => OldHistoryDataSourcesImpl(),
  );
  //home
  di.registerLazySingleton(
    () => HomeRemoteDatasourceImpl(sharedPreferences: di(), client: di()),
  );
  di.registerLazySingleton(
    () => CategoryLocalDataSourceImpl(),
  );
  //login
  di.registerLazySingleton(
    () => LoginRemoteDatasourceImpl(client: di()),
  );
  //login
  di.registerLazySingleton(
    () => LoginLocalDataSourceImpl(sharedPreferences: di()),
  );
  //auth
  di.registerLazySingleton(
    () => AuthRemoteDatasourceImpl(client: di()),
  );
  //auth
  di.registerLazySingleton(
    () => AuthLocalDataSourceImpl(sharedPreferences: di()),
  );
  //profile
  di.registerLazySingleton(
    () => ProfRemoteDatasourceImpl(sharedPreferences: di(), client: di()),
  );
  di.registerLazySingleton(
    () => ProfileLocalDataSourcesImpl(),
  );
  //selects
  di.registerLazySingleton(
    () => SelectPartRemoteDatasourceImpl(sharedPreferences: di(), client: di()),
  );
  di.registerLazySingleton<SelectPartLocalDatasource>(
    () => SelectPartLocalDatasourceImpl(),
  );
  //not send
  di.registerLazySingleton(
    () => NotSendDataSourcesImpl(),
  );
  di.registerLazySingleton<NotSendRemoteDatasource>(
    () => NotSendRemoteDatasourceImpl(client: di(), sharedPreferences: di()),
  );

  /// Image picker
  di.registerLazySingleton<ImagePickerUtils>(() => ImagePickerUtilsImpl());

  /// Location Service
  di.registerLazySingleton<LocationService>(() => LocationServiceImpl());

  /// Network
  di.registerLazySingleton<http.Client>(() => http.Client());
  di.registerLazySingleton<Dio>(() => Dio());

  /// Network Info
  di.registerLazySingleton(() => InternetConnectionChecker());

  di.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(di()));

  // home
  Hive.registerAdapter(CategoryModelAdapter());
  await Hive.openBox(categoryBox);

  Hive.registerAdapter(SubCategoryModelAdapter());
  await Hive.openBox(subCategoryBox);
  // new history
  Hive.registerAdapter(NewHistoryModelAdapter());
  await Hive.openBox(newHistoryBox);
  // old history
  Hive.registerAdapter(OldHistoryModelAdapter());
  await Hive.openBox(oldHistoryBox);
  // profile
  Hive.registerAdapter(ProfModelAdapter());
  await Hive.openBox(profileBox);
  // notSendData
  Hive.registerAdapter(NotSendModelAdapter());
  Hive.registerAdapter(ImgModelAdapter());

  var box = await Hive.openBox(prefs.getString("id") ?? forSendBox);
  print("${box.name} opened inside GET IT");
  forSendBox = prefs.getString("id") ?? forSendBox;

  ///Backing up old works if ID is not null
  // var boxOld = await Hive.openBox('for_send_box');
  //
  // if (sharedPreferences.getString('id') != null) {
  //   if (boxOld.length > 0) {
  //     var oldList = List.from(boxOld.values.toList().cast<NotSendModel>());
  //     await boxOld.deleteAll(boxOld.keys);
  //     await box.addAll(oldList);
  //     print("Copied from Old List ${oldList.length} to New List: ${box.length}");
  //   }
  //   forSendBox = sharedPreferences.getString("id") ?? forSendBox;
  // }

  ///Camera
  // Obtain a list of the available cameras on the device.
  var cameras = await availableCameras();
  di.registerLazySingleton<CameraDescription>(() => cameras.first);

  ///Update
  // Check for in app updates

  AppUpdateInfo _updateInfo = AppUpdateInfo(
      updateAvailability: UpdateAvailability.unknown,
      immediateUpdateAllowed: false,
      immediateAllowedPreconditions: [],
      flexibleUpdateAllowed: false,
      flexibleAllowedPreconditions: [],
      availableVersionCode: 0,
      installStatus: InstallStatus.unknown,
      packageName: 'com.example.uz',
      clientVersionStalenessDays: 0,
      updatePriority: 0);

  if (Platform.isAndroid) {
    try {
      _updateInfo = await InAppUpdate.checkForUpdate();
    } catch (e) {
      print("Error in 'In-AppUpdate:' " + e.toString());
    }
  }
  di.registerLazySingleton<AppUpdateInfo>(() => _updateInfo);

  ///Getting directories

  if (Platform.isAndroid) {
    Future<List<String>> getExternalPath() async {
      return await ExternalPath.getExternalStorageDirectories();
    }

    String downloadSoundDir;
    getExternalPath().then((value) {
      downloadSoundDir =
          value.first + "/" + ExternalPath.DIRECTORY_DOWNLOADS + '/sounds';
      di.registerLazySingleton<Directory>(() => Directory(downloadSoundDir));
    });
  } else {
    var dir = await getApplicationDocumentsDirectory();
    di.registerLazySingleton<Directory>(() => Directory(dir.path + "/sounds"));
  }
}
