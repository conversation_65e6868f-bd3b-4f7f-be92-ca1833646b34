import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/features/auth/domain/usescases/auth.dart';
import 'package:flutter/services.dart';
import 'package:meta/meta.dart';
import 'package:platform_device_id_plus/platform_device_id.dart';

part 'auth_event.dart';

part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthData authData;

  AuthBloc({required this.authData}) : super(AuthInitial()) {
    on<SendSMSEvent>(_sendSms, transformer: droppable());
  }

  FutureOr<void> _sendSms(SendSMSEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    final result = await authData(
      AuthParams(event.sms, event.tel, await getDeviceId()),
    );
    result.fold(
        (failure) => {
              if (failure is NoConnectionFailure)
                emit(AuthNoInternet())
              else if (failure is ServerFailure)
                emit(AuthFailure(failure.message))
              else if (failure is InputFormatterFailure)
                emit(AuthFailure(failure.message))
            },
        (r) => {
              if (r) {emit(AuthSuccess("Success"))} else {emit(AuthError())}
            });
  }

  getDeviceId() async {
    String? deviceId;
    try {
      deviceId = await PlatformDeviceId.getDeviceId;
      return deviceId;
    } on PlatformException {
      deviceId = 'Failed to get deviceId.';
      CustomToast.showToast(deviceId);
      return deviceId;
    }
  }
}
