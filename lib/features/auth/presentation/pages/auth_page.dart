// import 'package:android_sms_retriever/android_sms_retriever.dart';
import 'package:auto_sms_verification/auto_sms_verification.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/core/utils/target.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:etahlil/features/lock/presentation/pages/lock_page.dart';
import 'package:etahlil/features/login/presentation/pages/login_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import '../widgets/timer.dart';

class AuthPage extends StatefulWidget {
  final String tel;

  const AuthPage(this.tel, {Key? key}) : super(key: key);

  static Widget screen(String tel) => BlocProvider(
        create: (context) => di<AuthBloc>(),
        child: AuthPage(tel),
      );

  @override
  _AuthPageState createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage> {
  var maskFormatter = MaskTextInputFormatter(mask: '#   #   #   #');
  TextEditingController otpCode = TextEditingController();
  bool hasTimerStopped = false;

  late AuthBloc _bloc;

  void initSmsListener() async {
    String? message = await AutoSmsVerification.startListeningSms();
    var code = '';
    if (message != null) {
      code = getCode(message);
    }
    if (code != '') {
      otpCode.text = code;
      AutoSmsVerification.stopListening();
      _bloc.add(SendSMSEvent(code, widget.tel));
    }
  }

  String getCode(String sms) {
    try {
      final intRegex = RegExp(r'\d+', multiLine: true);
      final code = intRegex.allMatches(sms).first.group(0);
      return code ?? '';
    } catch (e) {
      return '';
    }
  }

  @override
  void initState() {
    if (isAndroid()) {
      initSmsListener();
    }
    _bloc = BlocProvider.of<AuthBloc>(context);
    super.initState();
  }

  @override
  void dispose() {
    otpCode.dispose();
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        backgroundColor: cWhiteColor,
        body: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              if (state is AuthNoInternet) {
                CustomToast.showToast(
                    "Интернет билан алоқа йўқ илтимос алоқани текширинг!");
              } else if (state is AuthFailure) {
                CustomToast.showToast(state.message);
              }
              if (state is AuthSuccess) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Navigator.pushReplacement(
                    context,
                    CupertinoPageRoute(
                        builder: (context) => PasswordScreen.screen()),
                  );
                });
              }
              if (state is AuthError) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    SvgPicture.asset(
                      "assets/icons/svgtahlil.svg",
                      color: cFirstColor,
                      width: 196.w,
                      height: 33.h,
                    ),
                    SizedBox(
                      height: 68.h,
                    ),
                    Container(
                      width: 274.w,
                      margin: EdgeInsets.only(bottom: 16.h),
                      child: Text(
                        "Юборилган код нотўғри илтимос қайтадан харакат қилинг",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 13.sp,
                            color: cRedColor,
                            fontFamily: "Regular"),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      width: 274.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(
                            color: cRedColor,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: TextFormField(
                          inputFormatters: [maskFormatter],
                          keyboardType: TextInputType.number,
                          cursorColor: cFirstColor,
                          controller: otpCode,
                          maxLines: 1,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: "─  ─  ─  ─",
                            hintStyle: TextStyle(
                                fontSize: 18.sp,
                                color: cGrayColor,
                                fontFamily: "Regular"),
                          ),
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: cRedColor,
                              fontFamily: "Regular"),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        var text = maskFormatter.getUnmaskedText();
                        if (text.length > 3) {
                          _bloc.add(SendSMSEvent(text, widget.tel));
                        } else {
                          CustomToast.showToast(
                              "4 тадан кам белги киритдингиз!");
                        }
                      },
                      child: _widget1(state),
                      color: cFirstColor,
                      elevation: 0,
                      minWidth: 274.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          CupertinoPageRoute(
                              builder: (context) => LoginPage.screen()),
                        );
                      },
                      child: Text(
                        'Қайта рақам киритиш',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontFamily: 'Regular',
                          color: cGrayColor,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/corp.svg",
                          height: 10.h,
                          width: 10.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              "v: " + version,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 28.h,
                    ),
                  ],
                );
              } else {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    SvgPicture.asset(
                      "assets/icons/svgtahlil.svg",
                      color: cFirstColor,
                      width: 196.w,
                      height: 33.h,
                    ),
                    SizedBox(
                      height: 68.h,
                    ),
                    Container(
                      width: 274.w,
                      margin: EdgeInsets.only(bottom: 16.h),
                      child: Text(
                        "Ушбу +998" +
                            widget.tel +
                            " рақамга 4 рақамдан иборат  маҳфий код юборилди, илтимос кодни киритинг!",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 13.sp,
                            color: cFirstColor,
                            fontFamily: "Regular"),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      width: 274.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(
                            color: cFirstColor,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: TextFormField(
                          inputFormatters: [maskFormatter],
                          keyboardType: TextInputType.number,
                          cursorColor: cFirstColor,
                          controller: otpCode,
                          maxLines: 1,
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: "─  ─  ─  ─",
                            hintStyle: TextStyle(
                                fontSize: 18.sp,
                                color: cGrayColor,
                                fontFamily: "Regular"),
                          ),
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: cFirstColor,
                              fontFamily: "Regular"),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        var text = maskFormatter.getUnmaskedText();
                        if (text.length > 3) {
                          _bloc.add(SendSMSEvent(text, widget.tel));
                        } else {
                          CustomToast.showToast(
                              "4 тадан кам белги киритдингиз!");
                        }
                      },
                      child: _widget1(state),
                      color: cFirstColor,
                      elevation: 0,
                      minWidth: 274.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    SizedBox(
                      height: 6.h,
                    ),
                    SizedBox(
                      height: 6.h,
                    ),
                    Visibility(
                      visible: hasTimerStopped,
                      child: TextButton(
                        onPressed: () {
                          Navigator.pushReplacement(
                            context,
                            CupertinoPageRoute(
                                builder: (context) => LoginPage.screen()),
                          );
                        },
                        child: Text(
                          'Қайта рақам киритиш',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontFamily: 'Regular',
                            color: cGrayColor,
                          ),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: !hasTimerStopped,
                      child: CountDownTimer(
                        secondsRemaining: 120,
                        whenTimeExpires: () {
                          setState(() {
                            hasTimerStopped = true;
                          });
                        },
                        countDownTimerStyle: TextStyle(
                          color: cRedColor,
                          fontSize: 17.0.sp,
                          height: 1.2.h,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/corp.svg",
                          height: 10.h,
                          width: 10.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              "v: " + version,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 28.h,
                    ),
                  ],
                );
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _widget1(state) {
    if (state is AuthLoading) {
      return const CupertinoActivityIndicator();
    } else {
      return Text(
        'Давом этиш',
        style: TextStyle(
          fontSize: 18.sp,
          fontFamily: 'Regular',
        ),
      );
    }
  }
}
