import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/features/auth/data/model/auth_model.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../kutilmoqda/data/model/not_send_model1.dart';

abstract class AuthLocalDataSource {
  Future<bool> setDataLocal(UserModel user);
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences sharedPreferences;

  AuthLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<bool> setDataLocal(UserModel user) async {
    try {
      //Open ID assigned box for works, cause:
      // - user ID is null (opening in di doesn't work)
      forSendBox = user.id.toString();
      var box = await Hive.openBox(forSendBox);

      ///Backing up old works cause ID is null (backing up in di doesn't work)
      // var boxOld = await Hive.openBox('for_send_box');
      // if (boxOld.length > 0) {
      //   var oldList = List.from(boxOld.values.toList().cast<NotSendModel>());
      //   await boxOld.deleteAll(boxOld.keys);
      //   await box.addAll(oldList);
      //   print("Copied from Old List ${oldList.length} to New List: ${box.length}");
      // }

      ///Saving BASE_URL
      var sessionManager = SessionManager();
      baseUrl = await sessionManager.get(BASE_URL);
      if (baseUrl != 'base') sharedPreferences.setString(BASE_URL, baseUrl);

      sharedPreferences.setString("id", user.id.toString());
      sharedPreferences.setString("name", user.name.toString());
      sharedPreferences.setString("login", user.login.toString());
      sharedPreferences.setString("phone", user.phoneNumber.toString());
      sharedPreferences.setString("provinceId", user.provinceId.toString());
      sharedPreferences.setString("regionId", user.regionId.toString());
      sharedPreferences.setString("sectorId", user.sectorId.toString());
      sharedPreferences.setString("token", user.token.toString());
      print("ID: ${user.id.toString()}, Login: ${user.login.toString()}");
      return true;
    } on LocalFailure {
      return false;
    }
  }
}
