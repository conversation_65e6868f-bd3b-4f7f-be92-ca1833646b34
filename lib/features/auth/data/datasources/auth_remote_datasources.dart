import 'dart:convert';

import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/features/auth/data/model/auth_model.dart';
import 'package:http/http.dart' as http;
import 'package:jwt_decoder/jwt_decoder.dart';

abstract class AuthRemoteDatasource {
  Future<dynamic> setData(String code, String tel, String mac);
}

class AuthRemoteDatasourceImpl implements AuthRemoteDatasource {
  final http.Client client;

  AuthRemoteDatasourceImpl({required this.client});

  @override
  Future<dynamic> setData(String code, String tel, String mac) async {
    try {
      var body = {"verifyCode": code, "phone": tel, "macAddress": mac};
      // CustomToast.showToast(mac);

      final response = await client.post(
        Uri.parse(baseUrl + authPath),
        body: jsonEncode(body),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
        },
      );

      // print(response.body);

      if (response.statusCode == 200) {
        final parsed = json.decode(response.body);
        String token = parsed["token"];
        Map<String, dynamic> decodedToken = JwtDecoder.decode(token);

        print(decodedToken.toString());
        // CustomToast.showToast(decodedToken.toString());

        var user = UserModel(
            id: decodedToken['_id'] ?? "",
            name:
                "${decodedToken['title']?['firstName'] ?? ""} ${decodedToken['title']?['lastName'] ?? ""} ${decodedToken['title']?['middleName'] ?? ""}",
            phoneNumber: tel,
            sectorId: decodedToken['title']?['sector'] ?? "",
            regionId: decodedToken['title']?['region'] ?? "",
            provinceId: decodedToken['title']?['province'] ?? "",
            token: token);

        return user;
      } else if (response.statusCode == 400) {
        return "0";
      } else if (response.statusCode == 404) {
        return "1";
      } else {
        return "2";
      }
    } on InputFormatterFailure {
      return InputFormatterFailure("Input Formatter error");
    }
  }
}
