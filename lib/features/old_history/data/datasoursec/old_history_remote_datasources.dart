import 'dart:convert';
import 'dart:developer';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/features/old_history/data/models/old_history_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../home/<USER>/models/category_part.dart';
import '../../../home/<USER>/models/done_part.dart';

abstract class OldHistoryRemoteDatasource {
  Future<List<OldHistoryModel>> getOldHistory(String categoryId,
      String subCategoryId, String startDate, String endDate);
}

class OldHistoryRemoteDatasourceImpl extends OldHistoryRemoteDatasource {
  final SharedPreferences sharedPreferences;

  final http.Client client;

  OldHistoryRemoteDatasourceImpl(
      {required this.sharedPreferences, required this.client});

  @override
  Future<List<OldHistoryModel>> getOldHistory(String categoryId,
      String subCategoryId, String startDate, String endDate) async {
    List<DonePart> list = [];
    List<OldHistoryModel> histories = [];
    List<CategoryPart> allCategoryList = await getCat();
    Map<String, String> body;

    if (startDate == "0" &&
        endDate == "0" &&
        categoryId == "0" &&
        subCategoryId == "0") {
      body = {};
    } else if (startDate == "0" || endDate == "0") {
      if (subCategoryId != "0")
        body = {"category": categoryId, "subCategory": subCategoryId};
      else
        body = {"category": categoryId};
    } else {
      if (subCategoryId != "0") {
        body = {
          "startDate": startDate,
          "endDate": endDate,
          "category": categoryId,
          "subCategory": subCategoryId
        };
      } else if (categoryId != "0") {
        body = {
          "startDate": startDate,
          "endDate": endDate,
          "category": categoryId,
        };
      } else {
        body = {"startDate": startDate, "endDate": endDate};
      }
    }

    try {
      final response = await client.post(
        Uri.parse(baseUrl + doneWorkPath),
        body: jsonEncode(body),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        final parsed = json.decode(response.body);
        for (int i = 0; i < (parsed["sendWorks"]?["data"] ?? []).length; i++) {
          list.add(DonePart.fromJson(parsed["sendWorks"]?["data"]?[i]));
        }

        list.forEach((c) {
          histories.add(OldHistoryModel(
            id: c.id,
            text: c.desc,
            date: c.uploadTime,
            title: c.subCategory,
            imgList: c.imgList,
          ));
        });

        //Mapping subCategory (work) title
        for (int i = 0; i < histories.length; i++) {
          allCategoryList.forEach((c) {
            if (histories[i].title == c.id) {
              histories[i].title = c.title;
            }
          });
        }

        return histories;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  Future<List<CategoryPart>> getCat() async {
    List<CategoryPart> catList = [];
    try {
      final response = await client.get(
        Uri.parse(baseUrl + categoriesPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed as List).length; i++) {
            catList.add(CategoryPart.fromJson(parsed[i]));
          }
          print("Category ------------------ " + catList.length.toString());
        } catch (e) {
          debugPrint(e.toString());
        }

        return catList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }
}
