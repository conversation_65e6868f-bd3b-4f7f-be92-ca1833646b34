import 'package:dartz/dartz.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/features/demo/demo_data.dart';
import 'package:etahlil/features/old_history/data/datasoursec/old_history_local_datasources.dart';
import 'package:etahlil/features/old_history/data/datasoursec/old_history_remote_datasources.dart';
import 'package:etahlil/features/old_history/data/models/old_history_model.dart';
import 'package:etahlil/features/old_history/domain/repository/old_history_repository.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../di/dependency_injection.dart';

class OldHistoryRepositoryImpl extends OldHistoryRepository {
  final OldHistoryRemoteDatasourceImpl oldHistoryRemoteDatasourceImpl;
  final OldHistoryDataSourcesImpl oldHistoryLocalDatasourceImpl;
  final NetworkInfo networkInfo;

  OldHistoryRepositoryImpl(
      {required this.oldHistoryRemoteDatasourceImpl,
      required this.oldHistoryLocalDatasourceImpl,
      required this.networkInfo});

  @override
  Future<Either<Failure, List<OldHistoryModel>>> getOldHistory(
      String categoryId,
      String subCategoryId,
      String startDate,
      String endDate,
      bool refresh) async {
    SharedPreferences sharedPreferences = di();
    bool isDemo = sharedPreferences.getBool("isDemo") ?? false;
    var time = (DateFormat('dd-MM-yyyy  HH:mm:ss').format(DateTime.now()));

    if (!isDemo) {
      if (await networkInfo.isConnected && refresh) {
        try {
          final result = await oldHistoryRemoteDatasourceImpl.getOldHistory(
              categoryId, subCategoryId, startDate, endDate);
          if (categoryId == "0" &&
              subCategoryId == "0" &&
              startDate == "0" &&
              endDate == "0") oldHistoryLocalDatasourceImpl.setOldHistory(result);

          sharedPreferences.setString("dateOld", time);

          return Right(result);
        } on ServerFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      } else {
        try {
          final result = await oldHistoryLocalDatasourceImpl.getOldHistory();
          return Right(result);
        } on LocalFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      }
    } else {
      return Right(getOldHistoryDemo());
    }
  }
}
