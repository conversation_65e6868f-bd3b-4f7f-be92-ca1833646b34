import 'package:etahlil/core/utils/api_path.dart';
import 'package:hive/hive.dart';

part 'old_history_model.g.dart';

@HiveType(typeId: 3)
class OldHistoryModel extends HiveObject {
  @HiveField(0)
  String? id;
  @HiveField(1)
  String? title;
  @HiveField(2)
  String? text;
  @HiveField(3)
  List<ImageList>? imageList;
  @HiveField(4)
  String? regionName;
  @HiveField(5)
  String? sectorId;
  @HiveField(6)
  String? sectorName;
  @HiveField(7)
  String? date;
  @HiveField(8)
  String? orinbosarIshtirokida;
  @HiveField(9)
  int? newCount;
  @HiveField(10)
  int? sendedCount;
  @HiveField(11)
  List<String>? imgList = [];

  OldHistoryModel(
      {this.id,
      this.title,
      this.text,
      this.imageList,
      this.regionName,
      this.sectorId,
      this.sectorName,
      this.date,
      this.orinbosarIshtirokida,
      this.newCount,
      this.sendedCount,
      this.imgList});
}

class ImageList {
  String? image;
  String? latLang;
  String? time;

  ImageList({this.image, this.latLang, this.time});
}
