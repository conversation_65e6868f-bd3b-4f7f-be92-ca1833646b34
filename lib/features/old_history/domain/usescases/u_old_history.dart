import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/usescases/usecase.dart';
import 'package:etahlil/features/old_history/data/models/old_history_model.dart';
import 'package:etahlil/features/old_history/domain/repository/old_history_repository.dart';

class UOldHistory extends UseCase<List<OldHistoryModel>, OldHistoryParams> {
  final OldHistoryRepository oldHistoryRepo;

  UOldHistory({required this.oldHistoryRepo});

  @override
  Future<Either<Failure, List<OldHistoryModel>>> call(OldHistoryParams params) {
    return oldHistoryRepo.getOldHistory(params.categoryId, params.subCategoryId,
        params.startDate, params.endDate, params.refresh);
  }
}

class OldHistoryParams extends Equatable {
  final String startDate;
  final String endDate;
  final String categoryId;
  final String subCategoryId;
  final bool refresh;

  const OldHistoryParams(
      {required this.categoryId,
      required this.subCategoryId,
      required this.startDate,
      required this.endDate,
      required this.refresh});

  @override
  List<Object?> get props => [categoryId, subCategoryId, startDate, endDate, refresh];
}
