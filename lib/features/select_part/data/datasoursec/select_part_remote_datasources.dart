import 'dart:convert';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/features/home/<USER>/models/category_model1.dart';
import 'package:etahlil/features/home/<USER>/models/category_part.dart';
import 'package:etahlil/features/home/<USER>/models/sub_category_model.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

abstract class SelectPartRemoteDatasource {
  Future<List<CategoryModel>> getSelectPart(int userId);

  Future<List<SubCategoryModel>> getSelectSubPart(String categoryId);
}

class SelectPartRemoteDatasourceImpl extends SelectPartRemoteDatasource {
  final SharedPreferences sharedPreferences;
  final http.Client client;

  SelectPartRemoteDatasourceImpl(
      {required this.sharedPreferences, required this.client});

  @override
  Future<List<CategoryModel>> getSelectPart(int userId) async {
    List<CategoryPart> list = [];
    List<CategoryPart> categoryList = [];
    List<CategoryModel> categories = [];

    try {
      final response = await client.get(
        Uri.parse(baseUrl + categoriesPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        final parsed = json.decode(response.body);
        for (int i = 0; i < (parsed["data"] as List).length; i++) {
          list.add(CategoryPart.fromJson(parsed["data"][i]));
        }

        //Getting only Categories
        for (int i = 0; i < list.length; i++) {
          if (list[i].parent == null) {
            categoryList.add(list[i]);
          }
        }

        //Mapping to original Model
        categoryList.forEach((c) {
          categories.add(CategoryModel(
              isCheck: false, id: c.id, name: c.title, description: c.desc));
        });

        return categories;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  @override
  Future<List<SubCategoryModel>> getSelectSubPart(String categoryId) async {
    List<CategoryPart> list = [];
    List<CategoryPart> subCategoryList = [];
    List<SubCategoryModel> subCategories = [];

    try {
      final response = await client.get(
        Uri.parse(baseUrl + categoriesPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        final parsed = json.decode(response.body);
        for (int i = 0; i < (parsed["data"] as List).length; i++) {
          list.add(CategoryPart.fromJson(parsed["data"][i]));
        }

        //Getting only SubCategories
        for (int i = 0; i < list.length; i++) {
          if (list[i].parent != null) {
            subCategoryList.add(list[i]);
          }
        }

        //Mapping to original Model
        subCategoryList.forEach((c) {
          subCategories.add(
              SubCategoryModel(id: c.id, name: c.title, categoryId: c.parent));
        });
        return subCategories;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }
}
