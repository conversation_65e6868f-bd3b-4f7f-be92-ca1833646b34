import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/features/home/<USER>/models/category_model1.dart';
import 'package:etahlil/features/home/<USER>/models/sub_category_model.dart';
import 'package:hive/hive.dart';

abstract class SelectPartLocalDatasource {
  Future<dynamic> getSelectPart(int userId);

  Future<dynamic> getSelectSubPart(String categoryId);
}

class SelectPartLocalDatasourceImpl extends SelectPartLocalDatasource {
  @override
  Future<dynamic> getSelectPart(int userId) async {
    try {
      final box = Hive.box(categoryBox);
      List<CategoryModel> list;
      List<CategoryModel> eventsFromHive =
          box.get(categoryBox)?.cast<CategoryModel>() ?? [];
      list = List.from(eventsFromHive);


      for (int i = 0; i < list.length; i++) {
        if (list[i].id == "0") {
          list.removeAt(i);
          break;
        }
      }

      for (int i = 0; i < list.length; i++) {
        if (list[i].id == "1") {
          list.removeAt(i);
          break;
        }
      }

      for (int i = 0; i < list.length; i++) {
        if (list[i].id == "2") {
          list.removeAt(i);
          break;
        }
      }

      return list;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<dynamic> getSelectSubPart(String categoryId) async {
    try {
      final box = Hive.box(subCategoryBox);
      List<SubCategoryModel> eventsFromHive =
          box.get(subCategoryBox)?.cast<SubCategoryModel>() ?? [];
      return eventsFromHive;
    } catch (e) {
      return [];
    }
  }
}
