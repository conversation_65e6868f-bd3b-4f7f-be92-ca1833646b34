import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';

abstract class PassLocalDataSource {
  Future<bool> setCompile(String pass, bool isNotSet);
}

class PassLocalDataSourceImpl implements PassLocalDataSource {
  final SharedPreferences sharedPreferences;

  PassLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<bool> setCompile(String pass, bool isNotSet) async {
    if (isNotSet) {
      saveShared(pin: pass);
      return true;
    } else {
      return sharedPreferences.getString('pin_code') == pass;
    }
  }

  Future saveShared({required String pin}) async {
    sharedPreferences.setString('pin_code', pin);
    debugPrint('SHARED ${sharedPreferences.get('pin_code')}');
  }
}
