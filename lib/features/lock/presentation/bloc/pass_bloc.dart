import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:dartz/dartz.dart';
import 'package:etahlil/features/lock/domain/usescases/u_lock.dart';
import 'package:flutter/cupertino.dart';

import '../../../../core/errors/failures.dart';

part 'pass_event.dart';
part 'pass_state.dart';

class PassBloc extends Bloc<PassEvent, PassState> {
  final Pass _pass;

  PassBloc({required Pass pass})
      : _pass = pass,
        super(const PassInitial("Махфий паролни киритинг")) {
    on<PassCompileEvent>(
      _nextPage,
      transformer: restartable(), //Unknown line
    );
    on<PassInitEvent>(
      _init,
      transformer: restartable(), //Unknown line
    );
    on<PassConfirmEvent>(
      _confirm,
      transformer: restartable(), //Unknown line
    );
  }

  FutureOr<void> _confirm(
      PassConfirmEvent event, Emitter<PassState> emit) async {
    emit(const PassConfirmState(message: "Киритилган паролни тасдиқланг"));
  }

  FutureOr<void> _init(PassInitEvent event, Emitter<PassState> emit) async {
    emit(const PassInitial(""));
  }

  FutureOr<void> _nextPage(
      PassCompileEvent event, Emitter<PassState> emit) async {
    Either<Failure, bool>? result;

    if (event.confirmPassController != "") {
      if (event.passController == event.confirmPassController) {
        emit(const PassLoading(""));
        result = await _pass(PasswordParams(event.passController, true));
      } else {
        emit(const PassError(
            errorMessage: "Пароллар мос емас, қайтадан бошланг",
            message: "Not Match Error"));
      }
    } else {
      emit(const PassLoading(""));
      result = await _pass(PasswordParams(event.passController, false));
    }

     result?.fold(
        (f) =>
            {emit(PassError(errorMessage: f.message, message: "Local Error"))},
        (s) => {
              if (s)
                {emit(const PassSuccess(""))}
              else
                {
                  emit(const PassError(
                      errorMessage:
                          "Киритилган пароль нотўғри илтимос қайтадан ҳаракат қилинг",
                      message: "Logic error"))
                }
            });

    // event.passController.clear();
    // event.confirmPassController.clear();
  }
}
