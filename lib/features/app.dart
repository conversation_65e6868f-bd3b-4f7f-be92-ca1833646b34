import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'lock/presentation/pages/lock_page.dart';
import 'login/presentation/pages/intro_page.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'navigation/navigation.dart';

class AppProvider extends StatelessWidget {
  final SharedPreferences sharedPreferences = di.get();

  AppProvider({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String id = sharedPreferences.getString('id') ?? "";
    bool isDemo = sharedPreferences.getBool('isDemo') ?? false;

    print(sharedPreferences.getString('token'));

    return ScreenUtilInit(
      splitScreenMode: true,
      builder: (BuildContext context, child) => GetMaterialApp(
        color: cFirstColor,
        builder: (context, widget) {
          ScreenUtil.init(
            context,
            designSize: const Size(428, 926),
            minTextAdapt: true,
          );
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
            child: widget!,
          );
        },
        debugShowCheckedModeBanner: false,
        home: isDemo
            ? const BottomNavigationPage()
            : id == ""
                ? IntroPage()
                : (sharedPreferences.getString('pin_code') ?? '') != ''
                    ? PasswordScreen.screen()
                    : const BottomNavigationPage(),
      ),
    );
  }
}
