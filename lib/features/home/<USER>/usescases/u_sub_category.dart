import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/usescases/usecase.dart';
import 'package:etahlil/features/home/<USER>/models/sub_category_model.dart';
import 'package:etahlil/features/home/<USER>/repositories/home_repository.dart';

class USubCategory
    extends UseCase<List<SubCategoryModel>, GetSubCategoryParams> {
  final HomeRepository homeRepository;

  USubCategory({required this.homeRepository});

  @override
  Future<Either<Failure, List<SubCategoryModel>>> call(
      GetSubCategoryParams params) {
    return homeRepository.getSubCategory(params.id!, params.refresh!);
  }
}

class GetSubCategoryParams extends Equatable {
  final String? id;
  final bool? refresh;

  const GetSubCategoryParams({required this.refresh, required this.id});

  @override
  List<Object?> get props => [id, refresh];
}
