class CategoryPart {
  CategoryPart({
      this.id, 
      this.title, 
      this.desc, 
      this.parent,});

  CategoryPart.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
    desc = json['desc'];
    parent = json['parent'];
  }
  String? id;
  String? title;
  String? desc;
  String? parent;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    map['desc'] = desc;
    map['parent'] = parent;
    return map;
  }

}