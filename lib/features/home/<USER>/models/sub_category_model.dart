import 'package:hive/hive.dart';

part 'sub_category_model.g.dart';

@HiveType(typeId: 1)
class SubCategoryModel extends HiveObject {
  @HiveField(0)
  String? id;
  @HiveField(1)
  String? name;
  @HiveField(2)
  String? categoryName;
  @HiveField(3)
  String? categoryId;
  @HiveField(4)
  int? countWorks;
  @HiveField(5)
  int? countPlan;
  @HiveField(6)
  String? status;
  @HiveField(7)
  String? today;
  @HiveField(8)
  String? todayAdded;

  SubCategoryModel(
      {this.id,
      this.name,
      this.categoryName,
      this.categoryId,
      this.countWorks,
      this.countPlan,
      this.status,
      this.today,
      this.todayAdded
      });
}
