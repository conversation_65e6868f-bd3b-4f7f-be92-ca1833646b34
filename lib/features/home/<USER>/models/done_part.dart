class DonePart {
  DonePart({
      this.id, 
      this.desc, 
      this.category, 
      this.subCategory,
      this.media, 
      this.uploadTime,
      this.imgList});

  DonePart.fromJson(dynamic json) {
    id = json['_id'];
    desc = json['desc'];
    category = json['category'];
    subCategory = json['subCategory'];
    if (json['media'] != null) {
      // imageList = <ImageList>[];
      json['media'].forEach((v) {
        imgList!.add(v["image"]);
      });
    }
    uploadTime = json['uploadTime'];
  }
  String? id;
  String? desc;
  String? category;
  String? subCategory;
  List<Media>? media;
  String? uploadTime;
  List<String>? imgList = [];

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['desc'] = desc;
    map['category'] = category;
    map['subCategory'] = subCategory;
    if (media != null) {
      map['media'] = media?.map((v) => v.toJson()).toList();
    }
    map['uploadTime'] = uploadTime;
    return map;
  }

}

class Media {
  Media({
      this.image,
      this.thumb,
      this.lat,
      this.lng,
      this.time,
      this.id,});

  Media.fromJson(dynamic json) {
    image = json['image'];
    thumb = json['thumb'];
    lat = json['lat'];
    lng = json['lng'];
    time = json['time'];
    id = json['_id'];
  }
  String? image;
  String? thumb;
  double? lat;
  double? lng;
  String? time;
  String? id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['image'] = image;
    map['thumb'] = thumb;
    map['lat'] = lat;
    map['lng'] = lng;
    map['time'] = time;
    map['_id'] = id;
    return map;
  }

}