class PlanPart {
  PlanPart({
      this.user, 
      this.category, 
      this.subCategory, 
      this.region, 
      this.sector, 
      this.repetition, 
      this.value,});

  PlanPart.fromJson(dynamic json) {
    user = json['user'];
    category = json['category'];
    subCategory = json['subCategory'];
    region = json['region'];
    sector = json['sector'];
    repetition = json['repetition'];
    value = json['value'];
  }
  String? user;
  String? category;
  String? subCategory;
  String? region;
  String? sector;
  int? repetition;
  int? value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['user'] = user;
    map['category'] = category;
    map['subCategory'] = subCategory;
    map['region'] = region;
    map['sector'] = sector;
    map['repetition'] = repetition;
    map['value'] = value;
    return map;
  }

}