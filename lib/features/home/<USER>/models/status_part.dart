class StatusPart {
  StatusPart({
      this.id, 
      this.subCategory, 
      this.user, 
      this.date,});

  StatusPart.fromJson(dynamic json) {
    id = json['_id'];
    subCategory = json['subCategory'];
    user = json['user'];
    date = json['date'];
  }
  String? id;
  String? subCategory;
  String? user;
  String? date;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['subCategory'] = subCategory;
    map['user'] = user;
    map['date'] = date;
    return map;
  }

}