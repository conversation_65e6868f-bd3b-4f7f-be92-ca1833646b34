import 'package:dartz/dartz.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/features/demo/demo_data.dart';
import 'package:etahlil/features/home/<USER>/datasources/home_local_datasources.dart';
import 'package:etahlil/features/home/<USER>/datasources/home_remote_datasources.dart';
import 'package:etahlil/features/home/<USER>/models/sub_category_model.dart';
import 'package:etahlil/features/home/<USER>/repositories/home_repository.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../di/dependency_injection.dart';

class HomeRepositoryImpl extends HomeRepository {
  final HomeRemoteDatasourceImpl homeRemoteDatasourceImpl;
  final CategoryLocalDataSourceImpl homeLocalDatasourceImpl;
  final NetworkInfo networkInfo;

  HomeRepositoryImpl(
      {required this.homeRemoteDatasourceImpl,
      required this.homeLocalDatasourceImpl,
      required this.networkInfo});

  @override
  Future<Either<Failure, dynamic>> getCategory(bool refresh) async {
    SharedPreferences sharedPreferences = di();
    bool isDemo = sharedPreferences.getBool("isDemo") ?? false;

    if (!isDemo) {
      if (await networkInfo.isConnected && refresh) {
        try {
          final result = await homeRemoteDatasourceImpl.getCategory();
          homeLocalDatasourceImpl.setCategory(result);
          return Right(result);
        } on ServerFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      } else {
        try {
          final result = await homeLocalDatasourceImpl.getCategory();
          return Right(result);
        } on LocalFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      }
    } else {
      return Right(getCategoryDemo());
    }
  }

  @override
  Future<Either<Failure, List<SubCategoryModel>>> getSubCategory(
      String id, bool refresh) async {
    SharedPreferences sharedPreferences = di();
    bool isDemo = sharedPreferences.getBool("isDemo") ?? false;

    if (!isDemo) {
      if (await networkInfo.isConnected && refresh) {
        try {
          final result = await homeRemoteDatasourceImpl.getSubCategory(id);
          homeLocalDatasourceImpl.setSubCategory(result);
          var time = (DateFormat('dd-MM-yyyy  HH:mm:ss').format(DateTime.now()));
          sharedPreferences.setString("dateHome", time);
          return Right(result);
        } on ServerFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      } else {
        try {
          final result = await homeLocalDatasourceImpl.getSubCategory()
              as List<SubCategoryModel>;
          return Right(result);
        } on LocalFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      }
    } else {
      return Right(getSubCategoryDemo());
    }
  }
}
