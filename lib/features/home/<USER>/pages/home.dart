import 'dart:async';
import 'dart:io';

import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/home/<USER>/bloc/category/category_bloc.dart';
import 'package:etahlil/features/home/<USER>/bloc/subCategory/sub_category_bloc.dart';
import 'package:etahlil/features/kutilmoqda/presentetion/pages/yuborilmagan.dart';
import 'package:etahlil/features/send_data/presentetion/pages/send_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import '../../../../core/utils/api_path.dart';
import '../../../login/presentation/pages/login_page.dart';
import '../../../profile/data/models/prof_model.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  static Widget screen() {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di<CategoryBloc>()),
        BlocProvider(create: (context) => di<SubCategoryBloc>())
      ],
      child: const HomePage(),
    );
  }

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  TextEditingController search = TextEditingController();

  late CategoryBloc _categoryBloc;
  late SubCategoryBloc _subCategoryBloc;
  var weekday = DateTime.now().weekday;
  SharedPreferences prefs = di();
  http.Client client = di();
  late HomeSuccessState state;
  bool isChecked = false;
  String selectedCategoryID = '';
  late String dateHome;

  AppUpdateInfo? _updateInfo = di();

  void showSnack(String text) {
    ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(behavior: SnackBarBehavior.floating, content: Text(text)));
  }

  @override
  void initState() {
    _categoryBloc = BlocProvider.of<CategoryBloc>(context);
    _subCategoryBloc = BlocProvider.of<SubCategoryBloc>(context);

    ///Adding page switch event
    var date = prefs.getString("dateHome");
    dateHome = date ?? "00-00-0000 00.00.00";
    _handleRefresh(date != null ? false : true);
    super.initState();
  }

  @override
  void dispose() {
    search.dispose();
    _subCategoryBloc.close();
    _categoryBloc.close();
    super.dispose();
  }

  Future _handleRefresh(bool refresh) async {
    _categoryBloc.add(GetCategory(refresh));
  }

  Future _onlineRefresh() async {
    _categoryBloc.add(GetCategory(true));
  }

  checkToken() async {
    try {
      final response = await client.get(
        Uri.parse(baseUrl + categoriesPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${prefs.getString("token")}"
        },
      );

      if (response.statusCode == 403) {
        Fluttertoast.showToast(
          msg: "Токен муддати тугади, қайта киринг!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.redAccent,
          textColor: Colors.white,
          fontSize: 20.0,
        );

        try {
          await prefs.remove('id');
          await prefs.remove('name');
          await prefs.remove('login');
          await prefs.remove('phone');
          await prefs.remove('regionId');
          await prefs.remove('sectorId');
          await prefs.remove('pin_code');
          await prefs.remove('token');
          await prefs.remove('isDemo');
          await prefs.remove(BASE_URL);

          await prefs.remove('dateHome');
          await prefs.remove('dateNew');
          await prefs.remove('dateOld');
          await prefs.remove('dateProf');

          Hive.box(profileBox).clear();
          Hive.box(categoryBox).clear();
          Hive.box(subCategoryBox).clear();
          Hive.box(newHistoryBox).clear();
          Hive.box(oldHistoryBox).clear();
          // Hive.box(forSendBox).clear();
        } catch (e) {}

        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (BuildContext context) => LoginPage.screen(),
          ),
          (Route route) => false,
        );
        throw HttpException('${response.statusCode}');
      }
    } on SocketException {
      // CustomToast.showToast('No Internet connection 😑');
    } on HttpException {
      // CustomToast.showToast('Error 403');
    } on FormatException {
      // CustomToast.showToast("Bad response format 👎");
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_updateInfo?.updateAvailability == UpdateAvailability.updateAvailable) {
      InAppUpdate.performImmediateUpdate().catchError((e) {
        showSnack(e.toString());
      });
    }

    return Container(
      color: cFirstColor,
      child: SafeArea(
        maintainBottomViewPadding: true,
        minimum: EdgeInsets.zero,
        child: Scaffold(
          backgroundColor: cBackColor,
          body: RefreshIndicator(
            onRefresh: _onlineRefresh,
            child: SingleChildScrollView(
              child: Container(
                height: MediaQuery.of(context).size.height - 110.h,
                child: Column(
                  children: [
                    BlocBuilder<CategoryBloc, CategoryState>(
                      builder: (context, state) {
                        if (!isChecked) {
                          checkToken();
                          isChecked = true;
                        }

                        if (state is HomeFailureState) {
                          CustomToast.showToast(
                              "Маълумотлар юкланишда хатолик юз берди!");
                        }

                        if (state is HomeFailureStateEmpty) {} //Unused state

                        if (state is HomeLoadingState) {
                          checkToken();
                          return Container(
                            height: state.isLarge ? (500).h : (306).h,
                            decoration: BoxDecoration(
                              color: cFirstColor,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(cRadius22.r),
                                bottomRight: Radius.circular(cRadius22.r),
                              ),
                            ),
                            child: const Center(
                                child: CupertinoActivityIndicator()),
                          );
                        } else if (state is HomeInitialState) {
                          return Container(
                            height: state.isLarge ? (500).h : (306).h,
                            decoration: BoxDecoration(
                                color: cFirstColor,
                                borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(cRadius22.r),
                                    bottomRight: Radius.circular(cRadius22.r))),
                            child: const Center(
                                child: CupertinoActivityIndicator()),
                          );
                        } else if (state is HomeSuccessState) {
                          this.state = state;

                          if (!state.dryRefresh) {
                            _subCategoryBloc.add(GetSubCategoryEvent(
                                refresh: state.refresh,
                                id: state.list[state.selected].id ?? '',
                                list: state.list));
                          }

                          selectedCategoryID =
                              state.list[state.selected].id ?? '';
                          print(state.list[state.selected].id!.toString() +
                              " = selectedCategoryID");

                          return Container(
                            height: state.isLarge ? (500).h : (306).h,
                            decoration: BoxDecoration(
                                color: cFirstColor,
                                borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(cRadius22.r),
                                    bottomRight: Radius.circular(cRadius22.r))),
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(vertical: 5.h),
                                  width: double.infinity,
                                  child: Text("Охирги янгиланиш: $dateHome",
                                      style: TextStyle(color: cWhiteColor)),
                                  alignment: Alignment.bottomCenter,
                                  decoration:
                                      BoxDecoration(color: Colors.green),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 25.w, vertical: 20.h),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      SizedBox(
                                        width: 35.w,
                                      ),
                                      const Spacer(),
                                      SizedBox(
                                        width: 250.w,
                                        child: Text(
                                          state.list[state.selected]
                                                  .description ??
                                              "",
                                          textAlign: TextAlign.center,
                                          maxLines: 3,
                                          style: TextStyle(
                                            color: cWhiteColor,
                                            fontFamily: 'Regular',
                                            fontSize: 15.sp,
                                          ),
                                        ),
                                      ),
                                      const Spacer(),
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            CupertinoPageRoute(
                                                builder: (context) =>
                                                    NotSendPage.screen()),
                                          ).then((value) {
                                            _handleRefresh(false);
                                            debugPrint('on back');
                                          });
                                        },
                                        child: Stack(
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(6.0),
                                              child: SvgPicture.asset(
                                                "assets/icons/cloud_icon.svg",
                                                width: 30.w,
                                                height: 30.h,
                                              ),
                                            ),
                                            Visibility(
                                              visible: state.count > 0,
                                              child: Positioned(
                                                top: 0,
                                                right: 0,
                                                child: Stack(
                                                  alignment: Alignment.center,
                                                  children: [
                                                    const Icon(
                                                        Icons.brightness_1,
                                                        size: 18.0,
                                                        color:
                                                            Colors.redAccent),
                                                    Text(
                                                      state.count.toString(),
                                                      style: TextStyle(
                                                          color: cWhiteColor,
                                                          fontSize: 14.sp,
                                                          fontFamily:
                                                              'SemiBold'),
                                                      textAlign:
                                                          TextAlign.center,
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 18.w),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Container(
                                          child: TextField(
                                            textAlign: TextAlign.start,
                                            autofocus: false,
                                            controller: search,
                                            cursorColor: cWhiteColor,
                                            decoration: InputDecoration(
                                              hintText: "Қидирув",
                                              border: InputBorder.none,
                                              hintStyle: TextStyle(
                                                  color: cBackColorIcon,
                                                  fontSize: 12.sp,
                                                  fontFamily: 'Medium'),
                                              prefixIconConstraints:
                                                  BoxConstraints(
                                                maxWidth: 25.w,
                                                maxHeight: 20.h,
                                                minHeight: 20.h,
                                                minWidth: 25.w,
                                              ),
                                              contentPadding:
                                                  EdgeInsets.only(bottom: 2.h),
                                              prefixIcon: SvgPicture.asset(
                                                "assets/icons/search_icon.svg",
                                                height: 20.h,
                                                width: 20.w,
                                              ),
                                            ),
                                            onChanged: (text) {
                                              _subCategoryBloc.add(
                                                  SearchSubCategoryEvent(
                                                      txt: text));
                                            },
                                            style: TextStyle(
                                                fontSize: 15.sp,
                                                fontFamily: 'Medium',
                                                color: cWhiteColor),
                                          ),
                                          height: 57.h,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 19.w),
                                          decoration: BoxDecoration(
                                              color: cSecondColor,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      cRadius16.r)),
                                        ),
                                        flex: 5,
                                      ),
                                      SizedBox(
                                        width: 12.w,
                                      ),
                                      Expanded(
                                        child: InkWell(
                                          onTap: () {
                                            _categoryBloc.add(ChangeColor(
                                                state.list,
                                                0,
                                                state.count,
                                                state.isLarge));
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        cRadius16.r),
                                                border: Border.all(
                                                    width: 1.5.w,
                                                    color: state.isLarge
                                                        ? cWhiteColor
                                                        : cSecondColor),
                                                color: cSecondColor),
                                            height: 57.h,
                                            width: 57.w,
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 17.w,
                                                vertical: 17.h),
                                            child: SvgPicture.asset(
                                              "assets/icons/lounch_icon.svg",
                                              height: 20.h,
                                              width: 20.w,
                                            ),
                                          ),
                                        ),
                                        flex: 1,
                                      ),
                                    ],
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  height: state.isLarge ? (300).h : (100).h,
                                  margin:
                                      EdgeInsets.only(left: 12.w, right: 12.w),
                                  child: GridView.builder(
                                      gridDelegate:
                                          SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: state.isLarge ? 3 : 1,
                                        mainAxisExtent: 100.w,
                                        crossAxisSpacing: 13.w,
                                        childAspectRatio: 1,
                                        mainAxisSpacing: 13.w,
                                      ),
                                      padding: const EdgeInsets.all(4.0),
                                      scrollDirection: state.isLarge
                                          ? Axis.vertical
                                          : Axis.horizontal,
                                      physics: const BouncingScrollPhysics(),
                                      itemCount: state.list.length,
                                      itemBuilder: (context, index) {
                                        return InkWell(
                                          onTap: () {
                                            _categoryBloc.add(ChangeColor(
                                                state.list,
                                                index,
                                                state.count,
                                                !state.isLarge));
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                shape: BoxShape.rectangle,
                                                border: Border.all(
                                                    width: 2.w,
                                                    color: state.list[index]
                                                                .id ==
                                                            "0"
                                                        ? cRedColor
                                                        : state.list[index]
                                                                    .id ==
                                                                "1"
                                                            ? cOrangeColor
                                                            : state.selected ==
                                                                    index
                                                                ? cWhiteColor
                                                                : cSecondColor),
                                                color: state.selected == index
                                                    ? cSecondColor
                                                    : cGrayColor3),
                                            width: 103.w,
                                            height: double.infinity,
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 2.w, vertical: 2.h),
                                            child: Center(
                                                child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  (state.list[index].count ??
                                                          "0")
                                                      .toString(),
                                                  maxLines: 1,
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: state.selected ==
                                                              index
                                                          ? cWhiteColor
                                                          : cSecondColor,
                                                      fontSize: 30.sp),
                                                ),
                                                Flexible(
                                                  child: Text(
                                                    state.list[index].name!,
                                                    maxLines: 2,
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                        color: state.selected ==
                                                                index
                                                            ? cWhiteColor
                                                            : cSecondColor,
                                                        fontFamily: 'Medium',
                                                        fontSize: 15.sp),
                                                  ),
                                                ),
                                              ],
                                            )),
                                          ),
                                        );
                                      }),
                                ),
                                const Spacer(),
                              ],
                            ),
                          );
                        } else {
                          return Container(
                            height: state.isLarge ? (500).h : (306).h,
                            decoration: BoxDecoration(
                                color: cFirstColor,
                                borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(cRadius22.r),
                                    bottomRight: Radius.circular(cRadius22.r))),
                            child: Column(
                              children: [
                                SizedBox(height: 45.h),
                                Container(
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 25.w),
                                  height: 60.h,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      SizedBox(
                                        width: 32.w,
                                      ),
                                      const Spacer(),
                                      SizedBox(
                                        width: 250.w,
                                        child: Text(
                                          "",
                                          textAlign: TextAlign.center,
                                          maxLines: 3,
                                          style: TextStyle(
                                            color: cWhiteColor,
                                            fontFamily: 'Regular',
                                            fontSize: 15.sp,
                                          ),
                                        ),
                                      ),
                                      const Spacer(),
                                      GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            CupertinoPageRoute(
                                                builder: (context) =>
                                                    NotSendPage.screen()),
                                          ).then((value) {
                                            _handleRefresh(false);
                                            debugPrint('on back');
                                          });
                                        },
                                        child: Stack(
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(6.0),
                                              child: SvgPicture.asset(
                                                "assets/icons/cloud_icon.svg",
                                                width: 24.w,
                                                height: 24.h,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 20.h,
                                ),
                                Container(
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 18.w),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Container(
                                          child: TextField(
                                            textAlign: TextAlign.start,
                                            autofocus: false,
                                            controller: search,
                                            cursorColor: cWhiteColor,
                                            decoration: InputDecoration(
                                              hintText: "Қидирув",
                                              border: InputBorder.none,
                                              hintStyle: TextStyle(
                                                  color: cBackColorIcon,
                                                  fontSize: 12.sp,
                                                  fontFamily: 'Medium'),
                                              prefixIconConstraints:
                                                  BoxConstraints(
                                                maxWidth: 25.w,
                                                maxHeight: 20.h,
                                                minHeight: 20.h,
                                                minWidth: 25.w,
                                              ),
                                              contentPadding:
                                                  EdgeInsets.only(bottom: 2.h),
                                              prefixIcon: SvgPicture.asset(
                                                "assets/icons/search_icon.svg",
                                                height: 20.h,
                                                width: 20.w,
                                              ),
                                            ),
                                            onChanged: (text) {
                                              _subCategoryBloc.add(
                                                  SearchSubCategoryEvent(
                                                      txt: text));
                                            },
                                            style: TextStyle(
                                                fontSize: 15.sp,
                                                fontFamily: 'Medium',
                                                color: cWhiteColor),
                                          ),
                                          height: 57.h,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 19.w),
                                          decoration: BoxDecoration(
                                              color: cSecondColor,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      cRadius16.r)),
                                        ),
                                        flex: 5,
                                      ),
                                      SizedBox(
                                        width: 12.w,
                                      ),
                                      Expanded(
                                        child: InkWell(
                                          onTap: () {
                                            _categoryBloc.add(ChangeColor(
                                                const [], 0, 0, state.isLarge));
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        cRadius16.r),
                                                border: Border.all(
                                                    width: 1.5.w,
                                                    color: state.isLarge
                                                        ? cWhiteColor
                                                        : cSecondColor),
                                                color: cSecondColor),
                                            height: 57.h,
                                            width: 57.w,
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 17.w,
                                                vertical: 17.h),
                                            child: SvgPicture.asset(
                                              "assets/icons/lounch_icon.svg",
                                              height: 20.h,
                                              width: 20.w,
                                            ),
                                          ),
                                        ),
                                        flex: 1,
                                      ),
                                    ],
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  height: state.isLarge ? (285).h : (85).h,
                                  margin: EdgeInsets.only(left: 18.w),
                                ),
                                const Spacer(),
                              ],
                            ),
                          );
                        }
                      },
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 18.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SvgPicture.asset(
                                "assets/icons/lounch_icon.svg",
                                height: 16.h,
                                width: 16.w,
                                color: cGrayColor2,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                "Категориялар",
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    fontFamily: "Medium",
                                    color: cGrayColor2),
                              ),
                            ],
                          ),
                          Text(
                            "(Юборилган | Режа)",
                            style: TextStyle(
                                fontSize: 14.sp,
                                fontFamily: "Medium",
                                color: cGrayColor2),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 6.h,
                    ),
                    Expanded(
                      child: BlocBuilder<SubCategoryBloc, SubCategoryState>(
                        builder: (context, state) {
                          if (state is SubCategoryFailureState) {
                            CustomToast.showToast(
                                "Маълумотлар юкланишда хатолик юз берди!");
                          }
                          if (state is SubCategoryLoadingState) {
                            return const Center(
                                child: CupertinoActivityIndicator());
                          } else if (state is SubCategorySuccessState) {

                            dateHome = prefs.getString("dateHome") ??
                                "00-00-0000 00.00.00";

                            if (!this.state.dryRefresh) {
                              // ignore: invalid_use_of_visible_for_testing_member
                              _categoryBloc.emit(HomeSuccessState(
                                  refresh: this.state.refresh,
                                  list: this.state.list,
                                  selected: this.state.selected,
                                  isLarge: this.state.isLarge,
                                  count: this.state.count,
                                  dryRefresh: true));
                            }

                            return RefreshIndicator(
                              onRefresh: _onlineRefresh,
                              child: Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: 18.w, vertical: 0),
                                child: ListView.builder(
                                  itemCount: state.list.length,
                                  itemBuilder: (context, index) {
                                    if (state.list.isNotEmpty)
                                      return GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: () {
                                          if (weekday != 7) {
                                            if (state.list[index].status ==
                                                "1") {
                                              Get.to(() => SendData.screen(
                                                  state.list[index].categoryId!,
                                                  state.list[index].id!,
                                                  state.list[index]
                                                      .categoryName!,
                                                  state.list[index]
                                                      .name!))?.then((value) {
                                                _handleRefresh(false);
                                                debugPrint('on back');
                                              });
                                            } else {
                                              if (!state.isState) {
                                                Get.to(() => SendData.screen(
                                                    state.list[index]
                                                        .categoryId!,
                                                    state.list[index].id!,
                                                    state.list[index]
                                                        .categoryName!,
                                                    state.list[index]
                                                        .name!))?.then((value) {
                                                  _handleRefresh(false);
                                                  debugPrint('on back');
                                                });
                                              } else {
                                                CustomToast.showToast(
                                                    "Илтимос аввал бажарилиши зарур бўлган ишларни бажаринг!");
                                              }
                                            }
                                          } else {
                                            CustomToast.showToast(
                                                "Якшанба куни иш жўната олмайсиз!");
                                          }
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      cRadius16.r),
                                              color: cWhiteColor),
                                          height: 80.h,
                                          margin: EdgeInsets.only(bottom: 12.h),
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 22.w),
                                          child: Row(
                                            children: [
                                              SvgPicture.asset(
                                                "assets/icons/ellipse.svg",
                                                height: 5.h,
                                                width: 5.w,
                                              ),
                                              SizedBox(
                                                width: 15.w,
                                              ),
                                              SizedBox(
                                                width: 250.w,
                                                child: Text(
                                                  state.list[index].name!,
                                                  maxLines: 2,
                                                  style: TextStyle(
                                                      fontSize: 16.sp,
                                                      color: cGrayColor2,
                                                      fontFamily: 'Medium'),
                                                ),
                                              ),
                                              const Spacer(),
                                              Text(
                                                selectedCategoryID != '1'
                                                    ? (state.list[index]
                                                            .countWorks
                                                            .toString() +
                                                        " | " +
                                                        state.list[index]
                                                            .countPlan
                                                            .toString())
                                                    : "0 | 1",
                                                maxLines: 1,
                                                style: TextStyle(
                                                    fontSize: 16.sp,
                                                    color: state.list[index]
                                                                .status ==
                                                            "1"
                                                        ? cRedColor
                                                        : state.list[index]
                                                                    .today ==
                                                                "2"
                                                            ? cDarkYellowColor
                                                            : cFirstColor,
                                                    fontFamily: 'Medium'),
                                              ),
                                              SizedBox(
                                                width: 6.w,
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    else {
                                      print(state);

                                      return Center(
                                        child: TextButton(
                                          onPressed: () async {
                                            _onlineRefresh();
                                          },
                                          child: Text(
                                            "Янгилаш...",
                                            style: TextStyle(
                                                fontSize: 18.sp,
                                                color: cGrayColor,
                                                fontFamily: 'Regular'),
                                          ),
                                        ),
                                      );
                                    }
                                  },
                                  physics: BouncingScrollPhysics(
                                      parent: AlwaysScrollableScrollPhysics()),
                                ),
                              ),
                            );
                          } else {
                            print(state);

                            return Center(
                              child: TextButton(
                                onPressed: () async {
                                  _onlineRefresh();
                                },
                                child: Text(
                                  "Янгилаш...",
                                  style: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: 'Regular'),
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
