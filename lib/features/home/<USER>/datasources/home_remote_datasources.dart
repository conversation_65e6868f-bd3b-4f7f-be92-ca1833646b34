import 'dart:convert';
import 'dart:developer';

import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/features/home/<USER>/models/category_model1.dart';
import 'package:etahlil/features/home/<USER>/models/category_part.dart';
import 'package:etahlil/features/home/<USER>/models/done_part.dart';
import 'package:etahlil/features/home/<USER>/models/status_part.dart';
import 'package:etahlil/features/home/<USER>/models/sub_category_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../models/plan_part.dart';

abstract class HomeRemoteDatasource {
  Future<List<CategoryModel>> getCategory();

  Future<List<SubCategoryModel>> getSubCategory(String id);
}

class HomeRemoteDatasourceImpl implements HomeRemoteDatasource {
  final SharedPreferences sharedPreferences;
  final http.Client client;

  HomeRemoteDatasourceImpl(
      {required this.sharedPreferences, required this.client});

  @override
  Future<List<CategoryModel>> getCategory() async {
    List<CategoryPart> allCategoryList = await getCat();
    List<CategoryPart> categoryList = [];
    List<CategoryPart> subCategoriesList = [];
    List<CategoryModel> categories = [];
    List<StatusPart> statusList = await getStatus();
    List<String> checkList = await getTodayAdded();
    List<PlanPart> countList = await getPlanCount();
    List<String> todayList = await getAllTillTodayWorks();

    Future<List<CategoryModel>> gather() async {
      if (allCategoryList.isNotEmpty) {
        //Extracting Categories
        for (int i = 0; i < allCategoryList.length; i++) {
          if (allCategoryList[i].parent == null) {
            categoryList.add(allCategoryList[i]);
          } else {
            subCategoriesList.add(allCategoryList[i]);
          }
        }

        int sl = statusList.length;
        int tl = todayList.length;
        int chl = checkList.length;

        //IMPORTANT
        if (statusList.isNotEmpty) {
          //Adding important category
          categories.add(CategoryModel(
              isCheck: false,
              id: "0",
              name: "Бажарилиши муҳим бўлган ишлар",
              description: "Бажарилиши муҳим бўлган ишлар",
              status: 0,
              repetition: "",
              count: statusList.length));
        }

        //Today added
        if (checkList.isNotEmpty) {
          //Adding important category
          categories.add(CategoryModel(
              isCheck: false,
              id: "1",
              name: "Бугунги иш режалар",
              description: "Бугунги иш режалар",
              status: 0,
              repetition: "",
              count: checkList.length));
        }

        //TODAY
        if (todayList.isNotEmpty) {
          int count = 0;

          //Identifying total count
          todayList.forEach((id) {
            countList.forEach((cou) {
              if (id == cou.subCategory && cou.value != null) {
                if (cou.value! > 0) {
                  count += cou.value!;
                }
              }
            });
          });

          //Adding today's category
          categories.add(CategoryModel(
              isCheck: false,
              id: "2",
              name: "Умумий иш реъжалар",
              description: "Умумий иш реъжалар",
              status: 0,
              repetition: "",
              count: count));
        }

        //Mapping to original Model
        categoryList.forEach((c) {
          categories.add(CategoryModel(
              isCheck: false,
              id: c.id,
              name: c.title,
              description: c.desc,
              status: 0,
              repetition: "",
              count: 0));
        });

        var offset = 0;
        if (sl > 0) {
          offset += 1;
        }
        if (tl > 0) {
          offset += 1;
        }

        if (chl > 0) {
          offset += 1;
        }

        //Mapping total count
        for (int i = offset; i < categories.length; i++) {
          int count = 0;
          countList.forEach((cou) {
            if (categories[i].id == cou.category && cou.value != null) {
              if (cou.value! > 0) {
                count += cou.value!;
              }
            }
          });
          categories[i].count = count;
        }

        return categories;
      } else {
        return [];
      }
    }

    categories = await gather();
    return categories;
  }

  @override
  Future<List<SubCategoryModel>> getSubCategory(String id) async {
    List<SubCategoryModel> subCategories = [];
    List<CategoryPart> allCategoryList = await getCat();
    List<CategoryPart> subCategoryList = [];
    List<StatusPart> statusList = await getStatus();
    List<String> checkList = await getTodayAdded();
    List<String> todayList = await getAllTillTodayWorks();

    List<PlanPart> countList = await getPlanCount();
    List<DonePart> doneList = await getDoneCount();

    if (allCategoryList.isNotEmpty) {
      //Getting only SubCategories
      for (int i = 0; i < allCategoryList.length; i++) {
        if (allCategoryList[i].parent != null) {
          subCategoryList.add(allCategoryList[i]);
        }
      }

      //Mapping to original Model
      subCategoryList.forEach((c) {
        subCategories.add(SubCategoryModel(
            id: c.id,
            name: c.title,
            categoryId: c.parent,
            categoryName: c.title,
            countWorks: 0,
            countPlan: 0,
            status: "0",
            today: "0"));
      });

      //Mapping plan count
      for (int i = 0; i < subCategories.length; i++) {
        countList.forEach((cou) {
          if (subCategories[i].id == cou.subCategory && cou.value != null) {
            subCategories[i].countPlan = cou.value;
          }
        });
      }

      //Mapping done count
      for (int i = 0; i < subCategories.length; i++) {
        int count = 0;
        doneList.forEach((cou) {
          if (subCategories[i].id == cou.subCategory) {
            count++;
          }
        });
        subCategories[i].countWorks = count;
      }

      //Mapping status
      subCategories.forEach((c) {
        statusList.forEach((s) {
          if (c.id == s.subCategory) {
            c.status = "1";
          }
        });
      });

      //Mapping today added
      subCategories.forEach((c) {
        checkList.forEach((s) {
          if (c.id == s) {
            c.todayAdded = "1";
          }
        });
      });

      //Mapping today's
      subCategories.forEach((c) {
        todayList.forEach((s) {
          if (c.id == s) {
            c.today = "2";
          }
        });
      });

      return subCategories;
    } else
      return [];
  }

  Future<List<CategoryPart>> getCat() async {
    List<CategoryPart> catList = [];
    try {
      final response = await client.get(
        Uri.parse(baseUrl + categoriesPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed as List).length; i++) {
            catList.add(CategoryPart.fromJson(parsed[i]));
          }
          print("Category ------------------ " + catList.length.toString());
        } catch (e) {
          debugPrint(e.toString());
        }

        return catList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  Future<List<StatusPart>> getStatus() async {
    List<StatusPart> statusList = [];

    try {
      final response = await client.get(
        Uri.parse(baseUrl + importantsPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed as List).length; i++) {
            if (parsed[i]["parent"] == null) {
              statusList.add(StatusPart.fromJson(parsed[i]));
            }
          }
          print("Status ------------------ " + statusList.length.toString());
        } catch (e) {
          debugPrint(e.toString());
        }

        return statusList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  Future<List<String>> getTodayAdded() async {
    List<String> todayAddedList = [];

    try {
      final response = await client.get(
        Uri.parse(baseUrl + plansByDayOnce),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed as List).length; i++) {
            todayAddedList.add(parsed[i]);
          }

          print("Today Added------------------ " +
              todayAddedList.length.toString());
        } on FormatException catch (e) {
          print("Format Error: " + e.message);
          return [];
        }

        return todayAddedList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  Future<List<DonePart>> getDoneCount() async {
    List<DonePart> doneList = [];

    try {
      final response = await client.post(
        Uri.parse(baseUrl + doneWorkPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed["newWorks"]?["data"] ?? []).length; i++) {
            doneList.add(DonePart.fromJson(parsed["newWorks"]?["data"][i]));
          }
          for (int i = 0;
              i < (parsed["sendWorks"]?["data"] ?? []).length;
              i++) {
            doneList.add(DonePart.fromJson(parsed["sendWorks"]?["data"][i]));
          }

          print("Done ------------------ " + doneList.length.toString());
        } catch (e) {
          debugPrint(e.toString());
        }

        return doneList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  Future<List<PlanPart>> getPlanCount() async {
    List<PlanPart> planList = [];

    try {
      final response = await client.get(
        Uri.parse(baseUrl + plansByUserPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed as List).length; i++) {
            if (parsed[i]["parent"] == null) {
              planList.add(PlanPart.fromJson(parsed[i]));
            }
          }

          print("Plan ------------------ " + planList.length.toString());
        } catch (e) {
          debugPrint(e.toString());
        }

        return planList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  Future<List<String>> getAllTillTodayWorks() async {
    List<String> todayList = [];

    try {
      final response = await client.get(
        Uri.parse(baseUrl + plansByDay),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed as List).length; i++) {
            todayList.add(parsed[i]);
          }

          print("Today ------------------ " + todayList.length.toString());
        } on FormatException catch (e) {
          print("Format Error: " + e.message);
          return [];
        }

        return todayList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }
}
