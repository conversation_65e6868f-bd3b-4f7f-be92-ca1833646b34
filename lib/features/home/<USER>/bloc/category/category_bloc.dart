import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/home/<USER>/models/category_model1.dart';
import 'package:etahlil/features/home/<USER>/usescases/u_category.dart';
import 'package:hive/hive.dart';
import 'package:meta/meta.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../../core/utils/app_constants.dart';

part 'category_event.dart';

part 'category_state.dart';

class CategoryBloc extends Bloc<HomeEvent, CategoryState> {
  final UCategory home;

  CategoryBloc({
    required this.home,
  }) : super(const HomeInitialState(isLarge: false)) {
    on<GetCategory>(getCategory, transformer: sequential());
    on<ChangeColor>(changeColor, transformer: sequential());
  }

  FutureOr<void> getCategory(
      GetCategory event, Emitter<CategoryState> emit) async {
    emit(const HomeLoadingState(isLarge: false));
    final result = await home(GetCategoryParams(event.refresh));
    final box = Hive.box(forSendBox);
    SharedPreferences prefs = di();
    var dateHome = prefs.getString("dateHome") ?? "00-00-0000 00.00.00";
    result.fold(
        (failure) => {
              if (failure is NoConnectionFailure)
                {emit(const HomeFailureState(isLarge: false))}
              else if (failure is ServerFailure)
                {emit(const HomeFailureState(isLarge: false))}
            },
        (r) => {
              if (r.isEmpty)
                {emit(const HomeFailureStateEmpty(isLarge: false))}
              else
                {
                  emit(HomeSuccessState(
                    list: r,
                    refresh: event.refresh,
                    count: box.length,
                    selected: 0,
                    isLarge: false,
                    dryRefresh: false,
                  ))
                }
            });
  }

  FutureOr<void> changeColor(
      ChangeColor event, Emitter<CategoryState> emit) async {
    final box = Hive.box(forSendBox);

    emit(HomeSuccessState(
        list: event.list,
        selected: event.index,
        isLarge: !event.isLarge,
        count: box.length,
        refresh: false,
        dryRefresh: false));
  }
}
