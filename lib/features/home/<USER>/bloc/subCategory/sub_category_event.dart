part of 'sub_category_bloc.dart';

@immutable
abstract class SubCategoryEvent {}

class GetSubCategoryEvent extends SubCategoryEvent {
  final String id;
  final List<CategoryModel> list;
  final bool refresh;

  GetSubCategoryEvent(
      {required this.refresh, required this.id, required this.list});
}

class SearchSubCategoryEvent extends SubCategoryEvent {
  final String txt;

  SearchSubCategoryEvent({required this.txt});
}
