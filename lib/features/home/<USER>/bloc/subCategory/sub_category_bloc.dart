import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/features/home/<USER>/models/category_model1.dart';
import 'package:etahlil/features/home/<USER>/models/sub_category_model.dart';
import 'package:etahlil/features/home/<USER>/usescases/u_sub_category.dart';
import 'package:meta/meta.dart';

part 'sub_category_event.dart';

part 'sub_category_state.dart';

class SubCategoryBloc extends Bloc<SubCategoryEvent, SubCategoryState> {
  final USubCategory subCategory;

  List<SubCategoryModel> subList = [];
  bool isStatus = false;

  SubCategoryBloc({
    required this.subCategory,
  }) : super(SubCategoryInitial()) {
    on<GetSubCategoryEvent>(getSubCategory, transformer: sequential());
    on<SearchSubCategoryEvent>(filterFun, transformer: sequential());
  }

  FutureOr<void> getSubCategory(
      GetSubCategoryEvent event, Emitter<SubCategoryState> emit) async {
    subList.clear();
    emit(SubCategoryLoadingState());
    final result = await subCategory(
      GetSubCategoryParams(id: event.id, refresh: event.refresh),
    );
    await result.fold(
        (failure) => {
              if (failure is NoConnectionFailure)
                {emit(SubCategoryFailureState())}
              else if (failure is ServerFailure)
                {emit(SubCategoryFailureState())}
            }, (r) async {
      if (event.refresh) isStatus = false;

      if (r.isEmpty) {
        emit(SubCategoryFailureState());
      } else {
        await fun(r, event).then((value) {
          emit(SubCategorySuccessState(list: value[0], isState: value[1]));
        });
      }
    });
  }

  FutureOr<void> filterFun(
      SearchSubCategoryEvent event, Emitter<SubCategoryState> emit) async {
    emit(SubCategoryLoadingState());
    if (event.txt.isEmpty) {
      emit(SubCategorySuccessState(list: subList, isState: isStatus));
    } else {
      emit(SubCategorySuccessState(
          list: subList.where((element) {
            final titleLower = element.name!.toLowerCase();
            final searchLower = event.txt.toLowerCase();
            return titleLower.contains(searchLower);
          }).toList(),
          isState: isStatus));
    }
  }

  // Important category fetch function
  Future<List> fun(
      List<SubCategoryModel> list, GetSubCategoryEvent event) async {
    for (var i in list) {
      if (event.id == i.categoryId) {
        print("Not Important $isStatus");
        subList.add(i);
      } else if (event.id == "0") {
        if (i.status == "1") {
          subList.add(i);
          isStatus = true;
          print("Important $isStatus");
        }
      } else if (event.id == "2") {
        if (i.today == "2") {
          subList.add(i);
        }
      } else if (event.id == "1") {
        if (i.todayAdded == "1") {
          subList.add(i);
        }
      }
    }
    return Future.value([subList, isStatus]);
  }
}
