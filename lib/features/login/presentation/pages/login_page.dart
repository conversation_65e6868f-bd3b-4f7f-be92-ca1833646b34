import 'package:dio/dio.dart';
import 'package:etahlil/core/functions.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/auth/presentation/pages/auth_page.dart';
import 'package:etahlil/features/lock/presentation/pages/lock_page.dart';
import 'package:etahlil/features/login/data/province_model.dart';
import 'package:etahlil/features/login/presentation/bloc/login_bloc.dart';
import 'package:etahlil/features/login/presentation/widgets/custom_dropdown.dart';
import 'package:etahlil/features/navigation/navigation.dart';
import 'package:etahlil/generated/assets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  static Widget screen() => BlocProvider(
        create: (context) => di<LoginBloc>(),
        child: const LoginPage(),
      );

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  var maskFormatter = MaskTextInputFormatter(mask: '(##) ###-##-##');
  TextEditingController tel = TextEditingController();
  TextEditingController password = TextEditingController();
  late Future<List<ProvinceModel>> provincesInit;

  String macAddress = "";
  bool shouldRefresh = false;
  ProvinceModel? selectedProvince;

  late LoginBloc _bloc;

  @override
  void initState() {
    baseUrl = 'base';

    getProvincesInitFunc();

    _bloc = BlocProvider.of<LoginBloc>(context);
    super.initState();
  }

  @override
  void dispose() {
    tel.dispose();
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cWhiteColor,
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: BlocConsumer<LoginBloc, LoginState>(
          listener: (context, state) {
            // do stuff here based on Bloc's state
          },
          buildWhen: (previous, current) {
            if (current is NoConnectionLogin) {
              WidgetsBinding.instance.addPostFrameCallback((time) {
                Snack('Интернет билан алоқа йўқ, илтимос алоқани текширинг!',
                    context, cRedColor);
              });
            } else if (current is LoginFailure) {
              WidgetsBinding.instance.addPostFrameCallback((time) {
                Snack("Хатолик: ${current.message}", context, cRedColor);
              });
            }

            if (current is LoginSuccess) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.pushReplacement(
                  context,
                  CupertinoPageRoute(
                      builder: (context) =>
                          AuthPage.screen(maskFormatter.getUnmaskedText())),
                );
              });
            }
            if (current is OldUser) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.pushReplacement(
                  context,
                  CupertinoPageRoute(
                      builder: (context) => BottomNavigationPage()),
                );
              });
            }

            if (current is PasswordAccepted) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.pushReplacement(
                  context,
                  CupertinoPageRoute(
                      builder: (context) => PasswordScreen.screen()),
                );
              });
            }

            return true;
          },
          builder: (context, state) {
            if (state is NoUser) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 35.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    SvgPicture.asset(
                      "assets/icons/svgtahlil.svg",
                      color: cFirstColor,
                      width: 196.w,
                      height: 33.h,
                    ),
                    SizedBox(
                      height: 58.h,
                    ),
                    _futureDropDown(),
                    SizedBox(
                      height: 10.h,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(color: cRedColor, spreadRadius: 1),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/call.svg',
                              width: 24.w,
                              height: 24.h,
                              color: cFirstColor,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Text(
                              '+998',
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: cGrayColor,
                                  fontFamily: "Regular"),
                            ),
                            Expanded(
                              child: TextFormField(
                                inputFormatters: [maskFormatter],
                                keyboardType: TextInputType.phone,
                                cursorColor: cFirstColor,
                                controller: tel,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "(--)--- -- --",
                                  hintStyle: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: "Regular"),
                                  prefixIconConstraints: BoxConstraints(
                                    maxWidth: 30.w,
                                    maxHeight: 30.h,
                                    minHeight: 25.h,
                                    minWidth: 25.w,
                                  ),
                                ),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: cRedColor,
                                    fontFamily: "Regular"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      margin: EdgeInsets.only(bottom: 16.h),
                      child: Text(
                        "Бундай фойдаланувчи рўйхатдан ўтмаган! Дастурдан фойдаланиш учун администратор билан боғланинг!",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 12.sp,
                            color: cRedColor,
                            fontFamily: "Regular"),
                      ),
                    ),
                    MaterialButton(
                      onPressed: () => sendLogin(null),
                      child: _widget1(state),
                      color: cFirstColor,
                      elevation: 0,
                      minWidth: 360.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        launchCaller();
                      },
                      child: Text(
                        'Админстратор билан боғланиш',
                        style: TextStyle(
                            fontSize: 15.sp,
                            fontFamily: 'Regular',
                            color: cBlackColor),
                      ),
                      color: cBackButtonColor,
                      elevation: 0,
                      minWidth: 360.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/corp.svg",
                          height: 10.h,
                          width: 10.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              "v: " + version,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 28.h,
                    ),
                  ],
                ),
              );
            } else if (state is ServerError) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 35.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    SvgPicture.asset(
                      "assets/icons/svgtahlil.svg",
                      color: cFirstColor,
                      width: 196.w,
                      height: 33.h,
                    ),
                    SizedBox(
                      height: 58.h,
                    ),
                    _futureDropDown(),
                    SizedBox(
                      height: 10.h,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(color: cRedColor, spreadRadius: 1),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/call.svg',
                              width: 24.w,
                              height: 24.h,
                              color: cFirstColor,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Text(
                              '+998',
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: cGrayColor,
                                  fontFamily: "Regular"),
                            ),
                            Expanded(
                              child: TextFormField(
                                inputFormatters: [maskFormatter],
                                keyboardType: TextInputType.phone,
                                cursorColor: cFirstColor,
                                controller: tel,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "(--)--- -- --",
                                  hintStyle: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: "Regular"),
                                  prefixIconConstraints: BoxConstraints(
                                    maxWidth: 30.w,
                                    maxHeight: 30.h,
                                    minHeight: 25.h,
                                    minWidth: 25.w,
                                  ),
                                ),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: cRedColor,
                                    fontFamily: "Regular"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      margin: EdgeInsets.only(bottom: 16.h),
                      child: Text(
                        "Хатолик! СМС жўнатилмади :(",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 12.sp,
                            color: cRedColor,
                            fontFamily: "Regular"),
                      ),
                    ),
                    MaterialButton(
                      onPressed: () => sendLogin(null),
                      child: _widget1(state),
                      color: cFirstColor,
                      elevation: 0,
                      minWidth: 360.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    MaterialButton(
                      onPressed: () {
                        launchCaller();
                      },
                      child: Text(
                        'Админстратор билан боғланиш',
                        style: TextStyle(
                            fontSize: 15.sp,
                            fontFamily: 'Regular',
                            color: cBlackColor),
                      ),
                      color: cBackButtonColor,
                      elevation: 0,
                      minWidth: 360.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/corp.svg",
                          height: 10.h,
                          width: 10.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              "v: " + version,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 28.h,
                    ),
                  ],
                ),
              );
            } else if (state is PasswordRequired) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 35.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    SvgPicture.asset(
                      "assets/icons/svgtahlil.svg",
                      color: cFirstColor,
                      width: 196.w,
                      height: 33.h,
                    ),
                    SizedBox(
                      height: 58.h,
                    ),
                    _futureDropDown(),
                    SizedBox(
                      height: 10.h,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(color: cFirstColor, spreadRadius: 1),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/call.svg',
                              width: 24.w,
                              height: 24.h,
                              color: cFirstColor,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Text(
                              '+998',
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: cGrayColor,
                                  fontFamily: "Regular"),
                            ),
                            Expanded(
                              child: TextFormField(
                                inputFormatters: [maskFormatter],
                                keyboardType: TextInputType.phone,
                                cursorColor: cFirstColor,
                                controller: tel,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "(--)--- -- --",
                                  hintStyle: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: "Regular"),
                                  prefixIconConstraints: BoxConstraints(
                                    maxWidth: 30.w,
                                    maxHeight: 30.h,
                                    minHeight: 25.h,
                                    minWidth: 25.w,
                                  ),
                                ),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: cFirstColor,
                                    fontFamily: "Regular"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(color: cFirstColor, spreadRadius: 1),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              Assets.iconsParol,
                              width: 24.w,
                              height: 24.h,
                              color: cFirstColor,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Expanded(
                              child: TextFormField(
                                keyboardType: TextInputType.visiblePassword,
                                cursorColor: cFirstColor,
                                controller: password,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "Парол",
                                  hintStyle: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: "Regular"),
                                  prefixIconConstraints: BoxConstraints(
                                    maxWidth: 30.w,
                                    maxHeight: 30.h,
                                    minHeight: 25.h,
                                    minWidth: 25.w,
                                  ),
                                ),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: cFirstColor,
                                    fontFamily: "Regular"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    MaterialButton(
                      onPressed: () => sendLogin(password.text),
                      child: _widget1(state),
                      color: cFirstColor,
                      elevation: 0,
                      minWidth: 360.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/corp.svg",
                          height: 10.h,
                          width: 10.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              "v: " + version,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 28.h,
                    ),
                  ],
                ),
              );
            } else if (state is PasswordIncorrect) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 35.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    SvgPicture.asset(
                      "assets/icons/svgtahlil.svg",
                      color: cFirstColor,
                      width: 196.w,
                      height: 33.h,
                    ),
                    SizedBox(
                      height: 58.h,
                    ),
                    _futureDropDown(),
                    SizedBox(
                      height: 10.h,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(color: cFirstColor, spreadRadius: 1),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/call.svg',
                              width: 24.w,
                              height: 24.h,
                              color: cFirstColor,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Text(
                              '+998',
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: cGrayColor,
                                  fontFamily: "Regular"),
                            ),
                            Expanded(
                              child: TextFormField(
                                inputFormatters: [maskFormatter],
                                keyboardType: TextInputType.phone,
                                cursorColor: cFirstColor,
                                controller: tel,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "(--)--- -- --",
                                  hintStyle: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: "Regular"),
                                  prefixIconConstraints: BoxConstraints(
                                    maxWidth: 30.w,
                                    maxHeight: 30.h,
                                    minHeight: 25.h,
                                    minWidth: 25.w,
                                  ),
                                ),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: cFirstColor,
                                    fontFamily: "Regular"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(color: cFirstColor, spreadRadius: 1),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              Assets.iconsParol,
                              width: 24.w,
                              height: 24.h,
                              color: cFirstColor,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Expanded(
                              child: TextFormField(
                                keyboardType: TextInputType.visiblePassword,
                                cursorColor: cFirstColor,
                                controller: password,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "Парол",
                                  hintStyle: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: "Regular"),
                                  prefixIconConstraints: BoxConstraints(
                                    maxWidth: 30.w,
                                    maxHeight: 30.h,
                                    minHeight: 25.h,
                                    minWidth: 25.w,
                                  ),
                                ),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: cFirstColor,
                                    fontFamily: "Regular"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      margin: EdgeInsets.only(bottom: 16.h),
                      child: Text(
                        "Кирилган логин ёки парол хато!",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 12.sp,
                            color: cRedColor,
                            fontFamily: "Regular"),
                      ),
                    ),
                    MaterialButton(
                      onPressed: () => sendLogin(password.text),
                      child: _widget1(state),
                      color: cFirstColor,
                      elevation: 0,
                      minWidth: 360.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/corp.svg",
                          height: 10.h,
                          width: 10.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              "v: " + version,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 28.h,
                    ),
                  ],
                ),
              );
            } else {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 35.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    SvgPicture.asset(
                      "assets/icons/svgtahlil.svg",
                      color: cFirstColor,
                      width: 196.w,
                      height: 33.h,
                    ),
                    SizedBox(
                      height: 58.h,
                    ),
                    _futureDropDown(),
                    SizedBox(
                      height: 10.h,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cWhiteColor,
                        boxShadow: const [
                          BoxShadow(color: cFirstColor, spreadRadius: 1),
                        ],
                      ),
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      child: Center(
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/call.svg',
                              width: 24.w,
                              height: 24.h,
                              color: cFirstColor,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Text(
                              '+998',
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: cGrayColor,
                                  fontFamily: "Regular"),
                            ),
                            Expanded(
                              child: TextFormField(
                                inputFormatters: [maskFormatter],
                                keyboardType: TextInputType.phone,
                                cursorColor: cFirstColor,
                                controller: tel,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "(--)--- -- --",
                                  hintStyle: TextStyle(
                                      fontSize: 18.sp,
                                      color: cGrayColor,
                                      fontFamily: "Regular"),
                                  prefixIconConstraints: BoxConstraints(
                                    maxWidth: 30.w,
                                    maxHeight: 30.h,
                                    minHeight: 25.h,
                                    minWidth: 25.w,
                                  ),
                                ),
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: cFirstColor,
                                    fontFamily: "Regular"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    MaterialButton(
                      onPressed: () => sendLogin(null),
                      child: _widget1(state),
                      color: cFirstColor,
                      elevation: 0,
                      minWidth: 360.w,
                      height: 70.h,
                      textColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(cRadius16.r)),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/corp.svg",
                          height: 10.h,
                          width: 10.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            Text(
                              "v: " + version,
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontFamily: "Medium",
                                  color: cGrayColor),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 28.h,
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Future<List<ProvinceModel>> getProvinces() async {
    final Dio dio = di();
    // SharedPreferences prefs = di();
    List<ProvinceModel> provinces = [];

    try {
      final response = await dio.get(controlUrl + provincesPath,
          options: Options(headers: <String, String>{
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer ' + '${prefs.getString('token')}'
          }));
      final data = response.data;
      print(data.toString());
      if (response.statusCode == 200) {
        for (int i = 0; i < (data.length); i++) {
          provinces.add(ProvinceModel.fromJson(data[i]));
        }
        return provinces;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return [];
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        setState(() {
          shouldRefresh = true;
        });

        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return [];
    }
  }

  getProvincesInitFunc() async {
    final NetworkInfo networkInfo = di.get();
    OverlayState? state = Overlay.of(context);

    setState(() {
      provincesInit = getProvinces();
    });

    if (await networkInfo.isConnected) {
      setState(() {
        shouldRefresh = false;
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((time) {
        showTopSnackBar(
          state,
          CustomSnackBar.error(
            message:
                'Интернет билан алоқа йўқ илтимос алоқани қайта текширинг!',
          ),
        );
      });
      setState(() {
        shouldRefresh = true;
      });
    }
  }

  Widget _futureDropDown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 10.h),
          child: Text(
            'Ҳудуд:',
            style: TextStyle(
                fontSize: 18.sp, color: cFirstColor, fontFamily: "Regular"),
          ),
        ),
        FutureBuilder<dynamic>(
          future: provincesInit,
          builder: (context, snapshot) {
            List<ProvinceModel> provinces = [];

            if (snapshot.data.runtimeType == List<ProvinceModel>) {
              provinces = snapshot.data;
            }

            switch (snapshot.connectionState) {
              case ConnectionState.waiting:
                {
                  // Otherwise, display a loading indicator.
                  return Container(
                      height: 70.h,
                      width: double.infinity,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cFirstColor,
                        boxShadow: const [
                          BoxShadow(color: cFirstColor, spreadRadius: 1),
                        ],
                      ),
                      child: CupertinoActivityIndicator(
                        color: cWhiteColor,
                      ));
                }
              default:
                if (snapshot.hasError)
                  return Container(
                      height: 70.h,
                      padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius16.r),
                        color: cRedColor,
                        boxShadow: const [
                          BoxShadow(color: cRedColor, spreadRadius: 1),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          'Error!',
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: Colors.white,
                              fontFamily: "Regular"),
                        ),
                      ));
                else {
                  // If the Future is complete, display the preview.

                  return !shouldRefresh
                      ? CustomDropDown(
                          onSelected: (province) async {
                            print('Selected: ${province?.title}');
                            selectedProvince = province;
                            if (selectedProvince != null) {
                              var sessionManager = SessionManager();

                              baseUrl = province?.api ?? baseUrl;
                              if (baseUrl != 'base')
                                sessionManager.set(BASE_URL, baseUrl);
                              else {
                                CustomToast.showToast('Base URL is invalid!');
                              }
                            } else {
                              CustomToast.showToast('Province is invalid!');
                            }
                          },
                          list: snapshot.data)
                      : InkWell(
                          onTap: () {
                            getProvincesInitFunc();
                          },
                          child: Container(
                              height: 70.h,
                              width: double.infinity,
                              padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
                              decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(cRadius16.r),
                                color: cPurpleColor,
                                boxShadow: const [
                                  BoxShadow(
                                      color: cPurpleColor, spreadRadius: 1),
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  'Янгилаш',
                                  style: TextStyle(
                                      fontSize: 18.sp,
                                      color: Colors.white,
                                      fontFamily: "Regular"),
                                ),
                              )),
                        );
                }
            }
          },
        ),
      ],
    );
  }

  sendLogin(String? password) {
    var phone = maskFormatter.getUnmaskedText();
    if (phone.length > 8) {
      if (baseUrl != 'base')
        _bloc.add(SendLoginEvent(maskFormatter.getUnmaskedText(), password));
      else
        CustomToast.showToast('Ҳудудни танланг!');
    } else {
      CustomToast.showToast("Бу телефон рақами емас!");
    }
  }

  Widget _widget1(state) {
    if (state is LoginLoading) {
      return const CupertinoActivityIndicator();
    } else {
      return Text(
        'Давом этиш',
        style: TextStyle(
          fontSize: 18.sp,
          fontFamily: 'Regular',
        ),
      );
    }
  }
}
