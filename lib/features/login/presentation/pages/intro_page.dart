import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../core/utils/app_constants.dart';
import 'login_page.dart';

class IntroPage extends StatelessWidget {
  const IntroPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: cWhiteColor,
      body: Container(
        padding:
        EdgeInsets.only(left: 30.w, right: 30.w, top: 25.h, bottom: 25.h),
        constraints: BoxConstraints.expand(),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 100.h,
            ),
            Image(
              image: AssetImage("assets/images/logoTahlil.png"),
              height: 200.h,
            ),
            SvgPicture.asset("assets/icons/svgtahlil.svg"),
            SizedBox(
              height: 250.h,
            ),



            SizedBox(
              height: 30.h,
            ),
            Text(
              '"E-tahlil" мобил иловасига ҳуш келибсиз!',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 20.sp, fontFamily: "Medium", color: cBlackColor),
            ),
            SizedBox(
              height: 30.h,
            ),
            MaterialButton(
              onPressed: () {
                Navigator.pushReplacement(
                    context,
                    CupertinoPageRoute(
                      builder: (context) => LoginPage.screen(),
                    ));
              },
              child: Text("Бошлаш"),
              color: cFirstColor,
              elevation: 0,
              minWidth: 360.w,
              height: 70.h,
              textColor: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(cRadius16.r)),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                    style: TextStyle(
                        fontSize: 10.sp,
                        fontFamily: "Medium",
                        color: cGrayColor),
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  Text(
                    "v: " + version,
                    style: TextStyle(
                        fontSize: 10.sp,
                        fontFamily: "Medium",
                        color: cGrayColor),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}