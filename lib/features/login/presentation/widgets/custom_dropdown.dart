import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/features/login/data/province_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

typedef ProvinceToVoidFunc = void Function(ProvinceModel?);

class CustomDropDown extends StatefulWidget {
  const CustomDropDown(
      {super.key, required this.onSelected, required this.list});

  final ProvinceToVoidFunc onSelected;
  final List<ProvinceModel> list;

  @override
  State<CustomDropDown> createState() => _CustomDropDownState();
}

class _CustomDropDownState extends State<CustomDropDown> {
  ProvinceModel? selectedValue;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<ProvinceModel>(
        isExpanded: true,
        hint: Row(
          children: [
            Icon(
              Icons.list,
              size: 20.h,
              color: Colors.white,
            ),
            SizedBox(
              width: 4,
            ),
            Expanded(
              child: Text(
                'Ҳудудни танланг',
                style: TextStyle(
                    fontSize: 18.sp,
                    color: Colors.white,
                    fontFamily: "Regular"),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        items: widget.list
            .map((ProvinceModel item) => DropdownMenuItem<ProvinceModel>(
                  value: item,
                  child: Text(
                    item.title.toString(),
                    style: TextStyle(
                        fontSize: 18.sp,
                        color: Colors.white,
                        fontFamily: "Regular"),
                    overflow: TextOverflow.ellipsis,
                  ),
                ))
            .toList(),
        value: selectedValue,
        onChanged: (value) {
          setState(() {
            selectedValue = value;
            if (selectedValue != null) widget.onSelected(selectedValue ?? null);
          });
        },
        buttonStyleData: ButtonStyleData(
          height: 70.h,
          padding: EdgeInsets.fromLTRB(15.w, 2.h, 5.w, 0.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(cRadius16.r),
            color: cFirstColor,
            boxShadow: const [
              BoxShadow(color: cFirstColor, spreadRadius: 1),
            ],
          ),
          // elevation: 2,
        ),
        iconStyleData: const IconStyleData(
          icon: Icon(
            Icons.arrow_forward_ios_outlined,
          ),
          iconSize: 14,
          iconEnabledColor: Colors.white,
          iconDisabledColor: Colors.grey,
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 200,
          width: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            color: Colors.blueAccent,
          ),
          offset: const Offset(-20, 0),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all(6),
            thumbVisibility: MaterialStateProperty.all(true),
          ),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 40,
          padding: EdgeInsets.only(left: 14, right: 14),
        ),
      ),
    );
  }
}
