import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/login/domain/usescases/u_login.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:meta/meta.dart';
import 'package:platform_device_id_plus/platform_device_id.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'login_event.dart';

part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginData loginData;

  LoginBloc({required this.loginData}) : super(LoginInitial()) {
    on<SendLoginEvent>(_sendLogin, transformer: sequential());
  }

  FutureOr<void> _sendLogin(
      SendLoginEvent event, Emitter<LoginState> emit) async {
    SharedPreferences sharedPreferences = di();

    emit(LoginLoading());
    if (event.tel != "911234567") {
      final result = await loginData(
        LoginParams(event.tel, event.password, await getDeviceId()),
      );
      result.fold(
          (failure) => {
                if (failure is NoConnectionFailure)
                  emit(NoConnectionLogin())
                else if (failure is ServerFailure)
                  emit(LoginFailure(failure.message))
              }, (r) {
        if (r == "1") {
          emit(LoginSuccess("Success"));
        } else if (r == "0") {
          emit(NoUser());
        } else if (r == "2" || r == false) {
          emit(ServerError());
        } else if (r == "3") {
          emit(PasswordRequired());
        } else if (r == "incorrectPass") {
          emit(PasswordIncorrect());
        } else if (r) {
          emit(PasswordAccepted());
        }
      });
    } else {
      sharedPreferences.setBool("isDemo", true);
      emit(OldUser());
    }
  }

  getDeviceId() async {
    String? deviceId;
    try {
      deviceId = await PlatformDeviceId.getDeviceId;
      return deviceId;
    } on PlatformException {
      deviceId = 'Failed to get deviceId.';
      CustomToast.showToast(deviceId);
      return deviceId;
    }
  }
}
