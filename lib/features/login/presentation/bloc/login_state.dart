part of 'login_bloc.dart';

@immutable
abstract class LoginState {}

class LoginInitial extends LoginState {}

class LoginLoading extends LoginState {}

class NoConnectionLogin extends LoginState {}

class LoginFailure extends LoginState {
  final String message;

  LoginFailure(this.message);
}

class PasswordRequired extends LoginState {}
class PasswordIncorrect extends LoginState {}
class PasswordAccepted extends LoginState {}

class NoUser extends LoginState {}

class OldUser extends LoginState {}

class ServerError extends LoginState {}

class LoginSuccess extends LoginState {
  final String token;

  LoginSuccess(this.token);
}
