import 'dart:convert';

import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/features/auth/data/model/auth_model.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:http/http.dart' as http;

abstract class LoginRemoteDatasource {
  Future<dynamic> setData(String tel, String? password, String macAddress);
}

class LoginRemoteDatasourceImpl implements LoginRemoteDatasource {
  final http.Client client;

  LoginRemoteDatasourceImpl({required this.client});

  @override
  Future<dynamic> setData(
      String tel, String? password, String macAddress) async {
    try {
      Map<String, String> body;
      String url;

      if (password == null) {
        body = {
          "phone": tel,
          // "macAddress": macAddress,
        };

        url = baseUrl + loginPath;
      } else {
        body = {
          "phone": tel,
          "password": password,
        };

        url = baseUrl + passwordLoginPath;
      }

      // print(macAddress);

      final response = await client.post(
        Uri.parse(url),
        body: jsonEncode(body),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
        },
      );

      print(response.body);
      Map<dynamic, dynamic> parsed = json.decode(response.body);

      if (response.statusCode == 200) {
        if (parsed["token"] != null) {
          String token = parsed["token"];
          Map<String, dynamic> decodedToken = JwtDecoder.decode(token);

          // CustomToast.showToast(decodedToken.toString());

          var user = UserModel(
              id: decodedToken['_id'] ?? "",
              name:
                  "${decodedToken['title']?['firstName'] ?? ""} ${decodedToken['title']?['lastName'] ?? ""} ${decodedToken['title']?['middleName'] ?? ""}",
              phoneNumber: tel,
              sectorId: decodedToken['title']?['sector'] ?? "",
              regionId: decodedToken['title']?['region'] ?? "",
              provinceId: decodedToken['title']?['province'] ?? "",
              token: token);

          return user;
        }
      } else if (response.statusCode == 201) {
        if (parsed.containsKey("message")
            ? parsed["message"] == "success"
            : false) {
          return "1";
        } else {
          return showMessage(parsed, '2');
        }
      } else if (response.statusCode == 400) {
        return showMessage(parsed, '2');
      } else if (response.statusCode == 403) {
        return "incorrectPass";
      } else if (response.statusCode == 404) {
        if (parsed.containsKey("message")
            ? parsed["message"] == "app store"
            : false) {
          return "3";
        } else {
          return showMessage(parsed, '2');
        }
      } else {
        return showMessage(parsed, '0');
      }
    } on InputFormatterFailure catch (e) {
      return "500";
    } catch (e) {
      // CustomToast.showToast(e.toString());
      throw FormatException('Error on server response format!');
    }
  }
}

String showMessage(Map<dynamic, dynamic> parsed, String errorNo) {
  if (!parsed.containsKey("message")) {
    CustomToast.showToast(parsed.toString());
  } else {
    CustomToast.showToast(parsed['message'].toString());
  }
  return errorNo;
}
