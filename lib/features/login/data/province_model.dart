class ProvinceModel {
  ProvinceModel({
      this.active, 
      this.id, 
      this.title, 
      this.desc, 
      this.api, 
      this.status,});

  ProvinceModel.fromJson(dynamic json) {
    active = json['active'];
    id = json['_id'];
    title = json['title'];
    desc = json['desc'];
    api = json['api'];
    status = json['status'];
  }
  bool? active;
  String? id;
  String? title;
  String? desc;
  String? api;
  bool? status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['active'] = active;
    map['_id'] = id;
    map['title'] = title;
    map['desc'] = desc;
    map['api'] = api;
    map['status'] = status;
    return map;
  }

}