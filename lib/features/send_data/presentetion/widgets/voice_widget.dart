// ignore_for_file: prefer_const_constructors

import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/core/widgets/alert_dialog.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/send_data/presentetion/widgets/path_root.dart';
import 'package:etahlil/features/send_data/presentetion/widgets/player_widget.dart';
import 'package:flutter/material.dart';
// import 'package:social_media_recorder/audio_encoder_type.dart';
// import 'package:social_media_recorder/screen/social_media_recorder.dart';

class VoiceWidget extends StatefulWidget {
  final Function(String) onRecorded;
  final String root;

  const VoiceWidget({Key? key, required this.onRecorded, required this.root})
      : super(key: key);

  @override
  State<VoiceWidget> createState() => _VoiceWidgetState();
}

class _VoiceWidgetState extends State<VoiceWidget> {
  String localePath = "";
  Directory downloadSoundDir = di();

  @override
  void initState() {

    if (Platform.isAndroid)
      _androidVersion().then((value) {
        if (value < 29) {
          _clearCache();
        }
      });
    else {
      _clearCache();
    }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Container(
        height: 50,
        margin: EdgeInsets.only(left: 35, right: 5),
        decoration: BoxDecoration(
            color: cBackButtonColor, borderRadius: BorderRadius.circular(15.0)),
        child: Stack(children: [
          Align(
            alignment: Alignment.center,
            child: Wrap(children: [
              InkWell(
                onTap: () async {
                  if (localePath != "") {
                    var result = await showAlert(context) ?? false;
                    if (result) {
                      setState(() {
                        localePath = "";
                        widget.onRecorded(localePath);
                      });
                    }
                  }
                },
                child: Container(
                    margin: EdgeInsets.only(right: 30),
                    decoration: BoxDecoration(
                        color: localePath == ""
                            ? Colors.green.shade100
                            : Colors.red.shade100,
                        borderRadius: BorderRadius.circular(5)),
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 6, bottom: 6),
                      child: localePath == ""
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              children: const [
                                Text("Ёзилмаган",
                                    style: TextStyle(color: Colors.green)),
                                Padding(
                                  padding: EdgeInsets.only(left: 2),
                                  child: Icon(
                                    Icons.mic_off_outlined,
                                    color: Colors.green,
                                  ),
                                )
                              ],
                            )
                          : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: const [
                                Text("Ўчириш",
                                    style: TextStyle(color: Colors.red)),
                                Padding(
                                  padding: EdgeInsets.only(left: 2),
                                  child: Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                )
                              ],
                            ),
                    )),
              )
            ]),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Wrap(children: [
              Container(
                  decoration: BoxDecoration(
                      color: cFirstColor,
                      borderRadius: BorderRadius.circular(15)),
                  height: 40,
                  width: 40,
                  child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: Icon(
                      Icons.mic,
                      color: cWhiteColor,
                    ),
                  ))
            ]),
          ),
          // Align(
          //   alignment: Alignment.bottomRight,
          //   child: SocialMediaRecorder(
          //     storeSoundRecoringPath: this.downloadSoundDir.path,
          //
          //     /// Attributes are compile time
          //     backGroundColor: Colors.black.withOpacity(0.0),
          //     counterBackGroundColor: Colors.black.withOpacity(0.0),
          //     recordIconBackGroundColor: cFirstColor,
          //     recordIconWhenLockBackGroundColor: cFirstColor,
          //     slideToCancelText: "Бекор қилиш >",
          //     cancelText: "Бекор",
          //     recordIconWhenLockedRecord: Icon(Icons.save, color: Colors.white),
          //     recordIcon:
          //         Icon(Icons.mic_rounded, color: Colors.black.withOpacity(0.0)),
          //     sendRequestFunction: (soundFile, s) {
          //       setState(() {
          //         localePath = soundFile.path;
          //         widget.onRecorded(localePath);
          //       });
          //     },
          //     encode: AudioEncoderType.AAC,
          //   ),
          // ),
        ]),
      ),
      Container(
          decoration: BoxDecoration(
              color: cBackButtonColor,
              borderRadius: BorderRadius.circular(15.0)),
          padding: EdgeInsets.all(5),
          child:
              PlayerWidget(localePath: localePath, key: ValueKey(localePath))),
    ]);
  }

  Future<dynamic> _androidVersion() async {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    final androidInfo = await deviceInfoPlugin.androidInfo;
    return androidInfo.version.sdkInt;
  }

  _clearCache() async {
    final path = downloadSoundDir.path;
    final isExists = await Directory(path).exists();
    getFilePath().then((value) {
      if (isExists) {
        if (value.isNotEmpty) {
          final dir = Directory(path);
          dir.deleteSync(recursive: true);
          print("Voices are cleared!");
        }
      }
    });
  }
}

extension FileExtension on String {
  String getFileName() {
    final List<String> parts = this.split('/');
    if (parts.isNotEmpty) {
      return parts.last;
    } else {
      return '';
    }
  }
}
