
import 'dart:io';

import 'package:path_provider/path_provider.dart';

/// used to get the current store path
///
String initialStorePathRecord = "";

Future<String> getFilePath() async {
  String sdPath = "";
  if (Platform.isIOS) {
    Directory tempDir = await getTemporaryDirectory();
    sdPath = initialStorePathRecord.isEmpty ? tempDir.path : initialStorePathRecord;
  } else {
    sdPath = initialStorePathRecord.isEmpty
        ? "/storage/emulated/0/"
        : initialStorePathRecord;
  }
  return sdPath;
}