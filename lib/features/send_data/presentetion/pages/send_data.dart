import 'dart:async';
import 'dart:convert';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/photo/image_picker_utils.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/core/location/location_service.dart';
import 'package:etahlil/core/widgets/dialog_frame.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/send_data/data/models/img_model.dart';
import 'package:etahlil/features/send_data/presentetion/bloc/send_data_bloc.dart';
import 'package:etahlil/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_awesome_select_clone/flutter_awesome_select.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive/hive.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'package:sn_progress_dialog/sn_progress_dialog.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:dio/dio.dart';
import '../../../../core/network/network_info.dart';
import '../../../kutilmoqda/data/model/not_send_model1.dart';
import '../widgets/voice_widget.dart';
import '../widgets/choices.dart' as choices;

class SendData extends StatefulWidget {
  final String categoryId;
  final String subCategoryId;
  final String categoryName;
  final String subCategoryName;

  const SendData(
      {Key? key,
      required this.categoryId,
      required this.categoryName,
      required this.subCategoryId,
      required this.subCategoryName})
      : super(key: key);

  static Widget screen(String catId, String subCatId, String categoryName,
          String subCategoryName) =>
      BlocProvider(
        create: (context) => di<SendDataBloc>(),
        child: SendData(
          categoryId: catId,
          subCategoryId: subCatId,
          categoryName: categoryName,
          subCategoryName: subCategoryName,
        ),
      );

  @override
  _SendDataState createState() => _SendDataState();
}

class _SendDataState extends State<SendData> {
  File? _imageFile0,
      _imageFile1,
      _imageFile2,
      _imageFile3,
      _imageFile4,
      _imageFile5;
  String sana0 = "",
      sana1 = "",
      sana2 = "",
      sana3 = "",
      sana4 = "",
      sana5 = "",
      latLang0 = "";

  List<int>? imageBytes;
  String? imageString;
  TextEditingController subject = TextEditingController();
  TextEditingController text = TextEditingController();
  late SendDataBloc _bloc;
  bool checkOrin = false;
  late List<ImgModel> images = [];
  final customFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  SharedPreferences sharedPreferences = di.get();
  final NetworkInfo networkInfo = di.get();
  http.Client client = di.get();
  Dio dio = di.get();

  late ProgressDialog pd;
  final FocusNode unitCodeCtrlFocusNode = FocusNode();
  bool _shouldRequestPermission = false;
  late final AppLifecycleListener _listener;
  late AndroidDeviceInfo? androidInfo;

  S2SingleSelected? cause = S2SingleSelected(value: 'unselected');
  String? voice = "";
  String root = "";

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid && verSDK < 33
          ? Permission.storage
          : Permission.photos,
      Permission.location,
      Permission.camera,
      Permission.microphone,
    ].request();

    return statuses;
  }

  showCustomDialog() {
    WidgetsBinding.instance.addPostFrameCallback((time) {
      showDialog(
          context: context,
          builder: (context) => AllDialogSkeleton(
                child: Center(
                  child: Column(children: [
                    SizedBox(
                      height: 20.h,
                    ),
                    Text(
                      "\"Давом этиш\" тугмасини босинг ва кейинги чиқадиган ойнада керакли рухсатларни беринг, бу дастур тўлақонли ишлаши учун муҳим! \n \n (Еслатма! Агар давом этиш тугмасидан кейин ҳеч нима чиқмаса созламалардан қўлда рухсат беришингиз керак!)",
                      style: TextStyle(fontSize: 20.sp),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Icon(Icons.warning, size: 100.h, color: cRedColor),
                    SizedBox(
                      height: 20.h,
                    ),
                    MaterialButton(
                        child: Text(
                          "Давом этиш",
                          style: TextStyle(color: cWhiteColor),
                        ),
                        color: cFirstColor,
                        onPressed: () async {
                          openAppSettings();
                          Navigator.pop(context);
                        })
                  ]),
                ),
                title: "Танлаш",
                icon: Assets.iconsEllipse,
              ));
    });
  }

  void permissionWall() async {
    ///Get location

    latLang0 = await di<LocationService>().getLatLang();

    Map<Permission, PermissionStatus> statuses = {};
    var verSDK = androidInfo?.version.sdkInt ?? 0;
    statuses = await requestPermissions();

    if (statuses[Permission.location] != PermissionStatus.granted ||
        statuses[Platform.isAndroid && verSDK < 33
                ? Permission.storage
                : Permission.photos] !=
            PermissionStatus.granted ||
        statuses[Permission.camera] != PermissionStatus.granted ||
        statuses[Permission.microphone] != PermissionStatus.granted) {
      if (statuses[Permission.location] == PermissionStatus.permanentlyDenied ||
          statuses[Platform.isAndroid && verSDK < 33
                  ? Permission.storage
                  : Permission.photos] ==
              PermissionStatus.permanentlyDenied ||
          statuses[Permission.camera] == PermissionStatus.permanentlyDenied ||
          statuses[Permission.microphone] ==
              PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.camera]);
        print(statuses[Permission.storage]);
        print(statuses[Permission.microphone]);
        print(statuses[Permission.location]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog();
      } else {
        ///Points to the recursion
        // permissionWall();
        CustomToast.showToast('Керакли рухсатлар берилмади!');
      }
    } else {
      /// If everything is granted, do smth
    }
  }

  @override
  void initState() {
    // Initialize the AppLifecycleListener class and pass callbacks

    if (Platform.isAndroid) {
      androidInfo = di();
    } else {
      androidInfo = null;
    }

    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    permissionWall();

    _bloc = BlocProvider.of<SendDataBloc>(context);
    pd = ProgressDialog(context: context);
    super.initState();
  }

  @override
  void dispose() {
    _listener.dispose();

    subject.dispose();
    text.dispose();
    _bloc.close();
    super.dispose();
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() {
    if (_shouldRequestPermission) {
      permissionWall();
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => print('inactive');

  void _onHidden() => print('hidden');

  void _onPaused() async {
    _shouldRequestPermission = true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        behavior: HitTestBehavior.translucent,
        child: SingleChildScrollView(
          physics: const NeverScrollableScrollPhysics(),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 20.h),
                height: 120.h,
                decoration: BoxDecoration(
                    color: cFirstColor,
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(cRadius16.r),
                        bottomRight: Radius.circular(cRadius16.r))),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 10.h, horizontal: 10.w),
                        child: SvgPicture.asset(
                          "assets/icons/arrow_left.svg",
                          width: 24.w,
                          height: 24.h,
                        ),
                      ),
                    ),
                    const Spacer(),
                    SizedBox(
                      child: Text(widget.categoryName,
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: cWhiteColor,
                              fontFamily: 'Medium')),
                      width: 300.w,
                    ),
                    const Spacer(),
                    SizedBox(
                      width: 30.w,
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 24.h,
              ),
              Container(
                height: 125.h,
                margin: EdgeInsets.symmetric(horizontal: 18.w),
                child: Row(
                  children: [
                    pickedImage(_imageFile0, "0"),
                    SizedBox(
                      width: 12.w,
                    ),
                    pickedImage(_imageFile1, "1"),
                    SizedBox(
                      width: 12.w,
                    ),
                    pickedImage(_imageFile2, "2"),
                  ],
                ),
              ),

              /// Remove 3 containers
              // SizedBox(
              //   height: 12.h,
              // ),
              // Container(
              //   height: 125.h,
              //   margin: EdgeInsets.symmetric(horizontal: 18.w),
              //   child: Row(
              //     children: [
              //       pickedImage(_imageFile3, "3"),
              //       SizedBox(
              //         width: 12.w,
              //       ),
              //       pickedImage(_imageFile4, "4"),
              //       SizedBox(
              //         width: 12.w,
              //       ),
              //       pickedImage(_imageFile5, "5"),
              //     ],
              //   ),
              // ),
              SizedBox(
                height: 20.h,
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 18.h),
                decoration: BoxDecoration(
                    color: cBackColor,
                    borderRadius: BorderRadius.circular(cRadius12),
                    border: Border.all(color: cFirstColor, width: 1.5.w)),
                child: Column(
                  children: [
                    Container(
                      child: TextField(
                        focusNode: unitCodeCtrlFocusNode,
                        textAlign: TextAlign.start,
                        autofocus: false,
                        maxLines: 20,
                        onTap: () {
                          if (!unitCodeCtrlFocusNode.hasFocus)
                            unitCodeCtrlFocusNode.requestFocus();
                          else
                            unitCodeCtrlFocusNode.unfocus();
                        },
                        controller: text,
                        cursorColor: cGrayColor2,
                        decoration: InputDecoration(
                          hintText: "Матн",
                          border: InputBorder.none,
                          hintStyle: TextStyle(
                              color: cGrayColor,
                              fontSize: 15.sp,
                              fontFamily: 'Medium'),
                        ),
                        style: TextStyle(
                            fontSize: 15.sp,
                            fontFamily: 'Medium',
                            color: cGrayColor2),
                      ),
                      height: 200.h,
                      padding: EdgeInsets.symmetric(horizontal: 25.w),
                      decoration: BoxDecoration(
                          color: cBackButtonColor,
                          borderRadius: BorderRadius.circular(cRadius12.r)),
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    Container(
                      child: VoiceWidget(
                        onRecorded: (path) {
                          print("Callback: " + path);
                          Future.delayed(Duration(milliseconds: 1000), () {
                            // Delayed Saving (important)
                            var bytes = File(path).readAsBytesSync();
                            print(bytes);
                            voice = base64Encode(bytes);
                          });
                        },
                        root: root,
                      ),
                      decoration: BoxDecoration(
                          color: cBackButtonColor,
                          borderRadius: BorderRadius.circular(cRadius12.r)),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 12.h,
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                padding: EdgeInsets.symmetric(horizontal: 22.w),
                height: 69.h,
                decoration: BoxDecoration(
                    color: cBackColor,
                    borderRadius: BorderRadius.circular(cRadius12),
                    border: Border.all(color: cFirstColor, width: 1.5.w)),
                child: Theme(
                  data: ThemeData(unselectedWidgetColor: cGrayColor2),
                  child: CheckboxListTile(
                    title: Text(
                      "Ўринбосар иштирокида",
                      style: TextStyle(
                          fontSize: 18.sp,
                          color: cGrayColor2,
                          fontFamily: 'Regular'),
                    ),
                    value: checkOrin,
                    onChanged: (newValue) {
                      if (!checkOrin) {
                        showModalBottomSheet(
                            context: context,
                            builder: (context) => SmartSelect<String?>.single(
                                  title: 'Сабаб:',
                                  selectedValue: cause?.value,
                                  choiceItems: choices.causes,
                                  onChange: (selected) => setState(() {
                                    cause = selected;
                                    Navigator.of(context).pop();
                                  }),
                                  modalType: S2ModalType.bottomSheet,
                                  tileBuilder: (context, state) {
                                    return S2Tile.fromState(state,
                                        isTwoLine: true,
                                        leading: Icon(
                                          Icons.supervised_user_circle,
                                          size: 60.h,
                                          color: cFirstColor,
                                        ));
                                  },
                                )).then((value) {
                          if (cause?.value != 'unselected') {
                            checkOrin = newValue!;
                            setState(() {});
                          }
                        });
                      } else {
                        if (cause?.value != 'unselected') {
                          checkOrin = newValue!;
                          setState(() {});
                        }
                      }
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  ),
                ),
              ),
              SizedBox(
                height: 12.h,
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: MaterialButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          "Бекор қилиш",
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: cGrayColor2,
                              fontFamily: 'Regular'),
                        ),
                        color: cBackButtonColor,
                        elevation: 0,
                        height: 70.h,
                        textColor: cGrayColor2,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(cRadius12.r)),
                      ),
                      flex: 1,
                    ),
                    SizedBox(
                      width: 12.w,
                    ),
                    Expanded(
                      child: MaterialButton(
                        onPressed: () {
                          addFile();
                        },
                        child: Text(
                          "Юбориш",
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: cWhiteColor,
                              fontFamily: 'Regular'),
                        ),
                        color: cFirstColor,
                        elevation: 0,
                        height: 70.h,
                        textColor: cGrayColor2,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(cRadius12.r)),
                      ),
                      flex: 1,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 40.h,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget pickedImage(File? _imageFile, String key) {
    unitCodeCtrlFocusNode.unfocus();
    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () async {
          final picker = di<ImagePickerUtils>();
          var verSDK = androidInfo?.version.sdkInt ?? 0;

          // You can request multiple permissions at once.
          Map<Permission, PermissionStatus> statuses =
              await requestPermissions();

          if (statuses[Permission.location] == PermissionStatus.granted &&
              statuses[Permission.camera] == PermissionStatus.granted &&
              statuses[Permission.microphone] == PermissionStatus.granted &&
              statuses[Platform.isAndroid && verSDK < 33
                      ? Permission.storage
                      : Permission.photos] ==
                  PermissionStatus.granted) {
            if (key == "0") {
              _imageFile0 = File(await picker.selectImageFromCamera(context));
              _imageFile0?.path == "" ? _imageFile0 = null : _imageFile0;
              sana0 = customFormat.format(DateTime.now()).toString();
            } else if (key == "1") {
              _imageFile1 = File(await picker.selectImageFromCamera(context));
              _imageFile1?.path == "" ? _imageFile1 = null : _imageFile1;
              sana1 = customFormat.format(DateTime.now()).toString();
            } else if (key == "2") {
              _imageFile2 = File(await picker.selectImageFromCamera(context));
              _imageFile2?.path == "" ? _imageFile2 = null : _imageFile2;
              sana2 = customFormat.format(DateTime.now()).toString();
            } else if (key == "3") {
              _imageFile3 = File(await picker.selectImageFromCamera(context));
              _imageFile3?.path == "" ? _imageFile3 = null : _imageFile3;
              sana3 = customFormat.format(DateTime.now()).toString();
            } else if (key == "4") {
              _imageFile4 = File(await picker.selectImageFromCamera(context));
              _imageFile4?.path == "" ? _imageFile4 = null : _imageFile4;
              sana4 = customFormat.format(DateTime.now()).toString();
            } else if (key == "5") {
              _imageFile5 = File(await picker.selectImageFromCamera(context));
              _imageFile5?.path == "" ? _imageFile5 = null : _imageFile5;
              sana5 = customFormat.format(DateTime.now()).toString();
            }

            ///This line causes error
            setState(() {});
          } else {
            permissionWall();
          }
        },
        child: _imageFile == null || _imageFile.path == ""
            ? SvgPicture.asset('assets/icons/default_image.svg')
            : Stack(
                fit: StackFit.expand,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: Image.file(
                      _imageFile,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Align(
                    alignment: Alignment.topRight,
                    child: InkWell(
                      onTap: () {
                        if (key == "0") {
                          _imageFile0 = null;
                        } else if (key == "1") {
                          _imageFile1 = null;
                        } else if (key == "2") {
                          _imageFile2 = null;
                        } else if (key == "3") {
                          _imageFile3 = null;
                        } else if (key == "4") {
                          _imageFile4 = null;
                        } else if (key == "5") {
                          _imageFile5 = null;
                        }
                        setState(() {});
                      },
                      child: SvgPicture.asset(
                        "assets/icons/close_icon.svg",
                        width: 25.w,
                        height: 25.h,
                      ),
                    ),
                  ),
                ],
              ),
      ),
      flex: 1,
    );
  }

  void addFile() async {
    // pd.show(max: 100, msg: "Маълумотлар юкланмоқда...", msgFontSize: 17.sp);
    try {
      if (latLang0 == "0.00,0.00") {
        latLang0 = await di<LocationService>().getLatLang();
      }
      images.clear();
      if (_imageFile0 != null) {
        imageBytes = _imageFile0?.readAsBytesSync();
        imageString = base64Encode(imageBytes!);
        images
            .add(ImgModel(latLang: latLang0, sana: sana0, image: imageString));
      }
      if (_imageFile1 != null) {
        imageBytes = _imageFile1?.readAsBytesSync();
        imageString = base64Encode(imageBytes!);
        images
            .add(ImgModel(latLang: latLang0, sana: sana1, image: imageString));
      }
      if (_imageFile2 != null) {
        imageBytes = _imageFile2?.readAsBytesSync();
        imageString = base64Encode(imageBytes!);
        images
            .add(ImgModel(latLang: latLang0, sana: sana2, image: imageString));
      }
      if (_imageFile3 != null) {
        imageBytes = _imageFile3?.readAsBytesSync();
        imageString = base64Encode(imageBytes!);
        images
            .add(ImgModel(latLang: latLang0, sana: sana3, image: imageString));
      }
      if (_imageFile4 != null) {
        imageBytes = _imageFile4?.readAsBytesSync();
        imageString = base64Encode(imageBytes!);
        images
            .add(ImgModel(latLang: latLang0, sana: sana4, image: imageString));
      }
      if (_imageFile5 != null) {
        imageBytes = _imageFile5?.readAsBytesSync();
        imageString = base64Encode(imageBytes!);
        images
            .add(ImgModel(latLang: latLang0, sana: sana5, image: imageString));
      }

      bool isDemo = sharedPreferences.getBool("isDemo") ?? false;

      if (!isDemo) {
        if (images.isNotEmpty) {
          if (text.text.length < 5 && text.text.length > 0) {
            pd.close();
            CustomToast.showToast(
                "Aгар матн киритсангиз камида 5 белгидан иборат бўлиши керак");
          } else {
            pd.show(
                max: 100, msg: "Маълумотлар юкланмоқда...", msgFontSize: 17.sp);
            NotSendModel work = NotSendModel(
              userId: sharedPreferences.getString("id"),
              categoryId: widget.categoryId.toString(),
              subCategoryId: widget.subCategoryId.toString(),
              orinbosarIshtirokida: (checkOrin ? 1 : 0).toString(),
              cause: cause?.title,
              title: voice,
              text: text.text.toString(),
              categoryName: widget.categoryName.toString(),
              subCategoryName: widget.subCategoryName.toString(),
              imagesList: images,
            );
            try {
              final box = Hive.box(forSendBox);
              box.add(work);
              pd.close();
              Navigator.pop(context);
            } catch (e) {
              debugPrint(e.toString());
              pd.close();
              CustomToast.showToast("Маълумотлар юкланишда хатолик юз берди!");
            }

            // if(await networkInfo.isConnected){
            //   var json = jsonEncode(images.map((e) => e.toJson()).toList());
            //
            //   var body = {
            //     "user_id": sharedPreferences.getString("id")!,
            //     "category_id": widget.categoryId.toString(),
            //     "subCategory_id": widget.subCategoryId.toString(),
            //     "orinbosar_ishtirokida": (checkOrin ? 1 : 0).toString(),
            //     "title": subject.text.toString(),
            //     "text": text.text.toString(),
            //     "images_list": json,
            //   };
            //   try {
            //     Options options = Options(
            //       receiveDataWhenStatusError: true,
            //       headers: {
            //         "Content-Type": "application/json; charset=UTF-8",
            //         "Accept": "application/json",
            //         "Authorization": "Bearer ${sharedPreferences.getString("token")}"
            //       },
            //       receiveTimeout: 60 * 1000,
            //       sendTimeout: 60 * 1000,
            //     );
            //
            //     final response = await dio.post(
            //       baseUrl + worksPHP,
            //       data: body,
            //       options: options,
            //       onSendProgress: (int sent, int total) {
            //         pd.update(value: (sent / total * 100).round() - 2);
            //       },
            //     );
            //     if (response.statusCode == 200) {
            //       pd.close();
            //       Navigator.pop(context);
            //     } else {
            //       pd.close();
            //       CustomToast.showToast("Маълумотлар юкланишда хатолик юз берди!");
            //     }
            //   } catch (e) {
            //     debugPrint(e.toString());
            //     pd.close();
            //     CustomToast.showToast("Маълумотлар юкланишда хатолик юз берди!");
            //   }
            // } else {
            //
            // }
          }
        } else {
          CustomToast.showToast("Илтимос аввал маълумот киритинг!");
          pd.close();
        }
      } else {
        CustomToast.showToast("You cannot upload in Demo!");
        pd.close();
      }
    } on InputFormatterFailure catch (e) {
      debugPrint("Малумотлар юкланишда ҳатолик");
      pd.close();
      return;
    } catch (e) {
      CustomToast.showToast(
          "Малумотлар юкланишда ҳатолик.. Сервер формати хато!");
      pd.close();
    }
  }

  void updateCount(int round) async {
    pd.update(value: round);
  }
}

// _bloc.add(
//   SendDataToServerEvent(
//     userId: 2,
//     subId: widget.categoryId,
//     subCategoryId: widget.subCategoryId,
//     presenceOfDeputy: checkOrin ? 1 : 0,
//     title: subject.text,
//     text: text.text,
//     images: images,
//   ),
// );
