import 'dart:async';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gal/gal.dart';
import 'package:mno_zoom_widget/zoom_widget.dart';
import 'package:native_device_orientation/native_device_orientation.dart';

// A screen that allows users to take a picture using a given camera.
class TakePictureScreen extends StatefulWidget {
  final CameraDescription camera;

  const TakePictureScreen({
    Key? key,
    required this.camera,
  }) : super(key: key);

  @override
  TakePictureScreenState createState() => TakePictureScreenState();
}

class TakePictureScreenState extends State<TakePictureScreen> {
  CameraController? _controller;
  Future<void>? _initializeControllerFuture;

  List<String> split(String string, String separator, {int max = 0}) {
    var result = <String>[];

    if (separator.isEmpty) {
      result.add(string);
      return result;
    }

    while (true) {
      var index = string.indexOf(separator, 0);
      if (index == -1 || (max > 0 && result.length >= max)) {
        result.add(string);
        break;
      }

      result.add(string.substring(0, index));
      string = string.substring(index + separator.length);
    }

    return result;
  }

  @override
  void initState() {
    super.initState();
    // To display the current output from the Camera,
    // create a CameraController.
    _controller = CameraController(
      // Get a specific camera from the list of available cameras.
      widget.camera,
      // Define the resolution to use.
      ResolutionPreset.high,
      imageFormatGroup: ImageFormatGroup.yuv420,
    );

    // Next, initialize the controller. This returns a Future.
    _initializeControllerFuture = _controller?.initialize().then((value) {
      _controller?.setFlashMode(FlashMode.auto);
      _controller?.lockCaptureOrientation(DeviceOrientation.portraitUp);
    });
  }

  @override
  void dispose() {
    // Dispose of the controller when the widget is disposed.
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var callback;
    var ctx;

    return Scaffold(
      extendBody: true,
      appBar: AppBar(
        title: Text('Расмга олинг',
            style: TextStyle(
                fontSize: 20.sp, color: cWhiteColor, fontFamily: 'Medium')),
        backgroundColor: cFirstColor,
        iconTheme: IconThemeData(color: cWhiteColor),
      ),
      // Wait until the controller is initialized before displaying the
      // camera preview. Use a FutureBuilder to display a loading spinner
      // until the controller has finished initializing.
      body: NativeDeviceOrientationReader(
        useSensor: true,
        builder: (contextRotation) {
          ctx = contextRotation;

          return FutureBuilder<void>(
            future: _initializeControllerFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                // If the Future is complete, display the preview.
                return Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    child: CameraPreview(_controller!));
              } else {
                // Otherwise, display a loading indicator.
                return Center(child: CircularProgressIndicator());
              }
            },
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: FloatingActionButton(
          backgroundColor: cFirstColor,
          shape: CircleBorder(),
          child: Icon(Icons.camera, color: Colors.white),
          onPressed: () async {
            // Take the Picture in a try / catch block. If anything goes wrong,
            // catch the error.
            try {
              // Ensure that the camera is initialized.
              await _initializeControllerFuture;

              // Attempt to take a picture

              ///Workaround based answer in Github
              await Future.delayed(new Duration(milliseconds: 300));

              final image = await _controller?.takePicture();

              if (image != null) {
                NativeDeviceOrientation orientation =
                    NativeDeviceOrientationReader.orientation(ctx);

                int turns;
                switch (orientation) {
                  case NativeDeviceOrientation.landscapeLeft:
                    turns = -1;
                    break;
                  case NativeDeviceOrientation.landscapeRight:
                    turns = 1;
                    break;
                  case NativeDeviceOrientation.portraitDown:
                    turns = 2;
                    break;
                  default:
                    turns = 0;
                    break;
                }

                // If the picture was taken, display it on a new screen
                callback = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => DisplayPictureScreen(
                        // Pass the automatically generated path to
                        // the DisplayPictureScreen widget.
                        imagePath: image.path,
                        orientation: turns),
                  ),
                );

                if (callback != null) {
                  int turn = callback[1];

                  var modifiedPath =
                      split(image.path, image.name).first + "mod" + image.name;
                  XFile? rotatedResult =
                      await FlutterImageCompress.compressAndGetFile(
                    image.path,
                    modifiedPath,
                    rotate: turn == -1
                        ? -90
                        : turn == 1
                            ? 90
                            : 0,
                  );

                  if (rotatedResult != null) {
                    await Gal.putImage(rotatedResult.path);
                    Navigator.pop(context, rotatedResult.path);
                  }
                }
              }
            } catch (e) {
              // If an error occurs, log the error to the console.
              print(e);
            }
          }),
      bottomNavigationBar: BottomAppBar(
        color: cFirstColor,
        shape: CircularNotchedRectangle(),
        notchMargin: 4.0,
        child: new Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            IconButton(
              icon: Icon(
                Icons.menu,
                color: Colors.transparent,
              ),
              onPressed: () => () {},
            ),
            IconButton(
              icon: Icon(
                Icons.home,
                color: Colors.transparent,
              ),
              onPressed: () => {},
            ),
          ],
        ),
      ),
    );
  }
}

// A widget that displays the picture taken by the user.
class DisplayPictureScreen extends StatelessWidget {
  final String imagePath;
  final int orientation;

  const DisplayPictureScreen(
      {Key? key, required this.imagePath, required this.orientation})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBody: true,
        floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
        floatingActionButton: FloatingActionButton(
          backgroundColor: cFirstColor,
          shape: CircleBorder(),
          child: Icon(Icons.check_circle_outline, color: Colors.white),
          onPressed: () {
            Navigator.pop(context, [imagePath, orientation]);
          },
        ),
        appBar: AppBar(
            backgroundColor: cFirstColor,
            iconTheme: IconThemeData(color: Colors.white),
            title: Text('Олинган расм',
                style: TextStyle(
                    fontSize: 20.sp,
                    color: cWhiteColor,
                    fontFamily: 'Medium'))),
        bottomNavigationBar: BottomAppBar(
          color: cFirstColor,
          shape: CircularNotchedRectangle(),
          notchMargin: 4.0,
          child: new Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              IconButton(
                icon: Icon(
                  Icons.menu,
                  color: Colors.transparent,
                ),
                onPressed: () => () {},
              ),
              IconButton(
                icon: Icon(
                  Icons.home,
                  color: Colors.transparent,
                ),
                onPressed: () => {},
              ),
            ],
          ),
        ),
        body: Zoom(
          maxZoomWidth: 8000,
          maxZoomHeight: 8000,
          scrollWeight: 10.0,
          canvasColor: cFirstColor.withAlpha(60),
          centerOnScale: true,
          enableScroll: true,
          doubleTapZoom: true,
          zoomSensibility: 2.3,
          initZoom: 0.0,
          axis: Axis.horizontal,
          child: RotatedBox(
            quarterTurns: orientation,
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.contain,
                  image: FileImage(File(imagePath)),
                ),
              ),
            ),
          ),
        ));
  }
}

// // If the picture was taken, pop and return callback path.
