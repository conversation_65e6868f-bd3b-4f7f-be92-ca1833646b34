import 'img_model.dart';

class SendDataModel {
  String? userId;
  String? categoryId;
  String? subCategoryId;
  String? orinbosarIshtirokida;
  String? cause;
  String? title;
  String? text;
  List<ImgModel>? imagesList;

  SendDataModel(
      {required this.userId,
      required this.categoryId,
      required this.subCategoryId,
      required this.orinbosarIshtirokida,
      required this.cause,
      required this.title,
      required this.text,
      required this.imagesList});
}
