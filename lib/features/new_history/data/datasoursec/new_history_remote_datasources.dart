import 'dart:convert';
import 'dart:developer';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/features/home/<USER>/models/done_part.dart';
import 'package:etahlil/features/new_history/data/models/new_history_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../home/<USER>/models/category_part.dart';

abstract class NewHistoryRemoteDatasource {
  Future<List<NewHistoryModel>> getNewHistory();
}

class NewHistoryRemoteDatasourceImpl extends NewHistoryRemoteDatasource {
  final SharedPreferences sharedPreferences;

  final http.Client client;

  NewHistoryRemoteDatasourceImpl(
      {required this.sharedPreferences, required this.client});

  @override
  Future<List<NewHistoryModel>> getNewHistory() async {
    List<DonePart> list = [];
    List<NewHistoryModel> histories = [];
    List<CategoryPart> allCategoryList = await getCat();

    try {
      final response = await client.post(
        Uri.parse(baseUrl + doneWorkPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        final parsed = json.decode(response.body);
        for (int i = 0; i < (parsed["newWorks"]?["data"] ?? []).length; i++) {
          list.add(DonePart.fromJson(parsed["newWorks"]?["data"]?[i]));
        }

        list.forEach((c) {
          histories.add(NewHistoryModel(
            id: c.id,
            text: c.desc,
            date: c.uploadTime,
            title: c.subCategory,
            imgList: c.imgList,
          ));
        });

        //Mapping subCategory (work) title
        for (int i = 0; i < histories.length; i++) {
          allCategoryList.forEach((c) {
            if (histories[i].title == c.id) {
              histories[i].title = c.title;
            }
          });
        }

        return histories;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }

  Future<List<CategoryPart>> getCat() async {
    List<CategoryPart> catList = [];
    try {
      final response = await client.get(
        Uri.parse(baseUrl + categoriesPath),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200) {
        try {
          final parsed = json.decode(response.body);
          log('data11: $parsed');
          for (int i = 0; i < (parsed as List).length; i++) {
            catList.add(CategoryPart.fromJson(parsed[i]));
          }
          print("Category ------------------ " + catList.length.toString());
        } catch (e) {
          debugPrint(e.toString());
        }

        return catList;
      } else {
        return [];
      }
    } on InputFormatterFailure {
      return [];
    }
  }
}
