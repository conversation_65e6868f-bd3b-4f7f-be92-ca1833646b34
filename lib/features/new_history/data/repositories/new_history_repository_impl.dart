import 'package:dartz/dartz.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/features/new_history/data/datasoursec/new_history_local_datasources.dart';
import 'package:etahlil/features/new_history/data/datasoursec/new_history_remote_datasources.dart';
import 'package:etahlil/features/new_history/data/models/new_history_model.dart';
import 'package:etahlil/features/new_history/domain/repository/new_history_repository.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../di/dependency_injection.dart';
import '../../../demo/demo_data.dart';

class NewHistoryRepositoryImpl extends NewHistoryRepository {
  final NewHistoryRemoteDatasourceImpl newHistoryRemoteDatasourceImpl;
  final NewHistoryDataSourcesImpl newHistoryLocalDatasourceImpl;
  final NetworkInfo networkInfo;

  NewHistoryRepositoryImpl(
      {required this.newHistoryRemoteDatasourceImpl,
      required this.newHistoryLocalDatasourceImpl,
      required this.networkInfo});

  @override
  Future<Either<Failure, List<NewHistoryModel>>> getNewHistory(
      bool refresh) async {
    SharedPreferences sharedPreferences = di();
    bool isDemo = sharedPreferences.getBool("isDemo") ?? false;
    var time = (DateFormat('dd-MM-yyyy  HH:mm:ss').format(DateTime.now()));

    if (!isDemo) {
      if (await networkInfo.isConnected && refresh) {
        try {
          final result = await newHistoryRemoteDatasourceImpl.getNewHistory();
          newHistoryLocalDatasourceImpl.setNewHistory(result);

          sharedPreferences.setString("dateNew", time);

          return Right(result);
        } on ServerFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      } else {
        try {
          final result = await newHistoryLocalDatasourceImpl.getNewHistory();
          return Right(result);
        } on LocalFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      }
    } else {
      print(isDemo);
      return Right(getNewHistoryDemo());
    }
  }
}
