// ignore_for_file: prefer_const_constructors

import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/features/home/<USER>/pages/home.dart';
import 'package:etahlil/features/new_history/presentetion/pages/new_history.dart';
import 'package:etahlil/features/old_history/presentetion/pages/old_history.dart';
import 'package:etahlil/features/profile/presentation/pages/profile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BottomNavigationPage extends StatefulWidget {
  const BottomNavigationPage({Key? key}) : super(key: key);

  @override
  State<BottomNavigationPage> createState() => _BottomNavigationPageState();
}

class _BottomNavigationPageState extends State<BottomNavigationPage> {
  int _selectedIndex = 0;
  late List<Widget> _widgetOptions;

  @override
  void initState() {
    _widgetOptions = <Widget>[
      HomePage.screen(),
      NewHistory.screen(),
      OldHistory.screen(),
      Profile.screen(),
    ];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackColor,
      body: Center(
        child: _widgetOptions.elementAt(_selectedIndex),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: cFirstColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20)),
        ),
        child: BottomNavigationBar(
          items: <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/icons/home.svg',
                color: cBackColorIcon,
              ),
              activeIcon: SvgPicture.asset(
                'assets/icons/home.svg',
                color: cWhiteColor,
              ),
              label: 'Aсосий',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/icons/history.svg',
                color: cBackColorIcon,
              ),
              activeIcon: SvgPicture.asset(
                'assets/icons/history.svg',
                color: cWhiteColor,
              ),
              label: 'Кўрилмоқда',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/icons/icon_success.svg',
                color: cBackColorIcon,
              ),
              activeIcon: SvgPicture.asset(
                'assets/icons/icon_success.svg',
                color: cWhiteColor,
              ),
              label: 'Юборилган',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/icons/profile.svg',
                color: cBackColorIcon,
              ),
              activeIcon: SvgPicture.asset(
                'assets/icons/profile.svg',
                color: cWhiteColor,
              ),
              label: 'Профил',
            ),
          ],
          selectedLabelStyle: TextStyle(fontSize: 15.sp),
          unselectedLabelStyle: TextStyle(fontSize: 15.sp),
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          iconSize: 26,
          unselectedItemColor: cBackColorIcon,
          currentIndex: _selectedIndex,
          selectedItemColor: cWhiteColor,
          onTap: (index) {
            setState(() => _selectedIndex = index);
          },
        ),
      ),
    );
  }
}
