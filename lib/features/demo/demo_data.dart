import 'package:etahlil/features/home/<USER>/models/category_model1.dart';
import 'package:etahlil/features/new_history/data/models/new_history_model.dart';
import 'package:etahlil/features/old_history/data/models/old_history_model.dart';
import 'package:etahlil/features/profile/data/models/prof_model.dart';

import '../home/<USER>/models/sub_category_model.dart';

List<CategoryModel> getCategoryDemo() {
  List<CategoryModel> categories = [
    CategoryModel(
        isCheck: false,
        id: "00",
        name: "Маҳаллабай дафтарлар",
        repetition: "",
        status: 0,
        count: 6,
        description: "Маҳаллабай ишларни ташкил этиш"),
    CategoryModel(
        isCheck: false,
        id: "01",
        name: "Инвестиция,Экспорт,Бюджет",
        repetition: "",
        status: 0,
        count: 15,
        description:
            "Инвестица лойиҳалари, Экспорт,Бюджет масалалари, Жиноятчиликка қарши к…"),
    CategoryModel(
        isCheck: false,
        id: "02",
        name: "Мур<PERSON>жаатлар,таълим",
        repetition: "",
        status: 0,
        count: 13,
        description:
            "Фуқаролар ва тадбиркорлар мурожаати билан ишлаш,Мактабгача ва ҳалқ таь…"),
    CategoryModel(
        isCheck: false,
        id: "03",
        name: "Хизмат кўрсатиш",
        repetition: "",
        status: 0,
        count: 5,
        description:
            "Хизмат кўрсатиш соҳаси,Янги иш ўринлари яратиш, Маҳалла фуқаролари йиғинлари"),
    CategoryModel(
        isCheck: false,
        id: "04",
        name: "Обод қишлоқ, Обод махалла",
        repetition: "",
        status: 0,
        count: 7,
        description: "Обод қишлоқ, Обод махалла, Оилавий тадбиркорлик, Спорт"),
    CategoryModel(
        isCheck: false,
        id: "05",
        name: "Коммунал соҳа",
        repetition: "",
        status: 0,
        count: 3,
        description: "Коммунал соҳа бўйича масалалар"),
  ];

  return categories;
}

List<SubCategoryModel> getSubCategoryDemo() {
  List<SubCategoryModel> subCategories = [
    SubCategoryModel(
        id: "001",
        name: "Фуқаролар мурожаатлари",
        categoryId: "00",
        status: "",
        categoryName: "Фуқаролар мурожаатлари",
        countWorks: 2),
    SubCategoryModel(
        id: "001",
        name: "Тадбиркорлар мурожаатлари",
        categoryId: "01",
        status: "",
        categoryName: "Тадбиркорлар мурожаатлари",
        countWorks: 3),
    SubCategoryModel(
        id: "001",
        name: "Инвестиция лойиҳалари, Экспорт масалалари",
        categoryId: "02",
        status: "",
        categoryName: "Инвестиция лойиҳалари, Экспорт масалалари",
        countWorks: 4),
    SubCategoryModel(
        id: "001",
        name: "Бюджет масалалари",
        categoryId: "02",
        status: "",
        categoryName: "Бюджет масалалари",
        countWorks: 5),
    SubCategoryModel(
        id: "001",
        name: "Жиноятчиликга қарши курашиш тадбирлари",
        categoryId: "03",
        status: "",
        categoryName: "Жиноятчиликга қарши курашиш тадбирлари",
        countWorks: 1),
    SubCategoryModel(
        id: "001",
        name: "Умумтаълим мактаблар масалалари",
        categoryId: "03",
        status: "",
        categoryName: "Умумтаълим мактаблар масалалари",
        countWorks: 1),
    SubCategoryModel(
        id: "001",
        name: "Маҳалла фуқаролари йиғинлари",
        categoryId: "04",
        status: "",
        categoryName: "Маҳалла фуқаролари йиғинлари",
        countWorks: 4),
    SubCategoryModel(
        id: "001",
        name: "Янги иш ўринлари яратиш",
        categoryId: "04",
        status: "",
        categoryName: "Янги иш ўринлари яратиш",
        countWorks: 8),
    SubCategoryModel(
        id: "001",
        name: "Хизмат кўрсатиш сохаси",
        categoryId: "05",
        status: "",
        categoryName: "Хизмат кўрсатиш сохаси",
        countWorks: 5),
    SubCategoryModel(
        id: "001",
        name: "Электр тармоқлари масалалари",
        categoryId: "05",
        status: "",
        categoryName: "Электр тармоқлари масалалари",
        countWorks: 3),
    SubCategoryModel(
        id: "001",
        name: "Оилавий тадбиркорлик",
        categoryId: "00",
        status: "",
        categoryName: "Оилавий тадбиркорлик",
        countWorks: 2),
    SubCategoryModel(
        id: "001",
        name: "Обод қишлоқ ва Обод махалла",
        categoryId: "00",
        status: "",
        categoryName: "Обод қишлоқ ва Обод махалла",
        countWorks: 7),
    SubCategoryModel(
        id: "001",
        name: "Ободонлаштириш ишлари",
        categoryId: "01",
        status: "Ободонлаштириш ишлари",
        categoryName: "",
        countWorks: 3),
  ];

  return subCategories;
}

List<NewHistoryModel> getNewHistoryDemo() {
  List<NewHistoryModel> newHistories = [
    NewHistoryModel(
        id: "abc123",
        title: "Joylarda ishlar o'rganildi",
        text: "Daraxt kesilishiga oldini olindi",
        regionName: "Farg'ona shahar",
        sectorId: "abcd1234",
        sectorName: "2-sektor",
        orinbosarIshtirokida: "O'rinbosar ishtirokida",
        newCount: 1,
        sendedCount: 3,
        date: "2022-07-05",
        imgList: [
          "https://downloadhdwallpapers.in/wp-content/uploads/2018/10/Blurry-Gaussian-Blur-2560x1440.jpg",
          "https://downloadhdwallpapers.in/wp-content/uploads/2018/10/Blurry-Gaussian-Blur-2560x1440.jpg",
          "https://downloadhdwallpapers.in/wp-content/uploads/2018/10/Blurry-Gaussian-Blur-2560x1440.jpg"
        ]),
    NewHistoryModel(
        id: "abc123",
        title: "Tadbirkorlar masalalari o'rganildi",
        text: "Yer masalalari hal qilindi",
        regionName: "Farg'ona shahar",
        sectorId: "abcd1234",
        sectorName: "2-sektor",
        orinbosarIshtirokida: "O'rinbosar ishtirokida",
        newCount: 1,
        sendedCount: 3,
        date: "2022-07-06",
        imgList: [
          "https://www.graphicsfuel.com/wp-content/uploads/2013/08/blurred-texture-background05-preview.jpg",
          "https://www.graphicsfuel.com/wp-content/uploads/2013/08/blurred-texture-background05-preview.jpg",
          "https://www.graphicsfuel.com/wp-content/uploads/2013/08/blurred-texture-background05-preview.jpg"
        ])
  ];

  return newHistories;
}

List<OldHistoryModel> getOldHistoryDemo() {
  List<OldHistoryModel> oldHistories = [
    OldHistoryModel(
        id: "abc123",
        title: "Asfalt yotqizildi",
        text: "Shaharning yaroqsiz yo'llariga asfalt yotqizildi",
        regionName: "Farg'ona shahar",
        sectorId: "abcd1234",
        sectorName: "2-sektor",
        orinbosarIshtirokida: "O'rinbosar ishtirokida",
        newCount: 1,
        sendedCount: 3,
        date: "2022-07-05",
        imgList: [
          "https://www.needpix.com/file_download.php?url=https://storage.needpix.com/rsynced_images/bokeh-2399452_1280.jpg",
          "https://www.needpix.com/file_download.php?url=https://storage.needpix.com/rsynced_images/bokeh-2399452_1280.jpg",
          "https://www.needpix.com/file_download.php?url=https://storage.needpix.com/rsynced_images/bokeh-2399452_1280.jpg"
        ]),
    OldHistoryModel(
        id: "abc123",
        title: "Fuqaro masalalari hal etildi",
        text: "Temir daftariga kiritilgan fuqaroga ko'mak berildi",
        regionName: "Farg'ona shahar",
        sectorId: "abcd1234",
        sectorName: "2-sektor",
        orinbosarIshtirokida: "O'rinbosar ishtirokida",
        newCount: 1,
        sendedCount: 3,
        date: "2022-07-05",
        imgList: [
          "https://getflywheel.com/wp-content/uploads/2015/08/free-blurred-backgrounds.jpg",
          "https://getflywheel.com/wp-content/uploads/2015/08/free-blurred-backgrounds.jpg",
          "https://getflywheel.com/wp-content/uploads/2015/08/free-blurred-backgrounds.jpg"
        ]),

  ];

  return oldHistories;
}

ProfModel getProfDemo() {
  ProfModel prof = ProfModel(
      id: "id",
      name: "Мамасодиқов Расул Комилович",
      regionName: "Фарғона шаҳар",
      sectorId: "sectorId",
      phoneNumber: "911234567",
      viloyatBoyicha: 2,
      tumanBoyicha: 10,
      barchaJonatmalar: 65,
      sectorKotibiKormoqda: 3,
      rejaGrafikBoyicha: 15,
      bajarilishiKerakIshlar: 30);

  return prof;
}
