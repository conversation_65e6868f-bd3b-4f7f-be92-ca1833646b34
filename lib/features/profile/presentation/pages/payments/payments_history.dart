import 'package:dio/dio.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/features/profile/data/models/payment_info_model.dart';
import 'package:etahlil/features/profile/presentation/widgets/history_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/generated/assets.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PaymentsHistoryPage extends StatefulWidget {
  const PaymentsHistoryPage({Key? key}) : super(key: key);

  @override
  _PaymentsHistoryPageState createState() => _PaymentsHistoryPageState();
}

class _PaymentsHistoryPageState extends State<PaymentsHistoryPage> {
  late Future<List<Payments>> userPayments;
  late List<Payments> userPaymentsStatic;
  GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  checkNetwork() async {
    NetworkInfo networkInfo = di();

    if (!await networkInfo.isConnected) {
      CustomToast.showToast('Интернет алоқасини текширинг!');
    }
  }

  @override
  void initState() {
    userPayments = getUserPayments();
    checkNetwork();
    // userPaymentsStatic = [
    //   Payments(
    //       amount: "12 000",
    //       startDate: "2024-11-11",
    //       endDate: "2024-11-11",
    //       month: "3"),
    //   Payments(
    //       amount: "18 000",
    //       startDate: "2023-11-11",
    //       endDate: "2023-11-11",
    //       month: "12")
    // ];
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: cFirstColor,
      child: SafeArea(
        maintainBottomViewPadding: true,
        minimum: EdgeInsets.zero,
        child: Scaffold(
          backgroundColor: cBackColor,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  width: double.infinity,
                  padding:
                      EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
                  decoration: BoxDecoration(
                      gradient: cFirstGradient,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(cRadius22.r),
                          bottomRight: Radius.circular(cRadius22.r))),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          child: SvgPicture.asset(
                            "assets/icons/arrow_left.svg",
                            width: 24.w,
                            height: 24.h,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text("Тўловлар тарихи",
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          style: TextStyle(
                              fontSize: 18.sp,
                              color: cWhiteColor,
                              fontFamily: 'Medium')),
                      const Spacer(),
                      SizedBox(
                        width: 20.w,
                      )
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 9,
                child: Center(
                  child: FutureBuilder<List<Payments>>(
                      future: userPayments,
                      builder: (BuildContext context,
                          AsyncSnapshot<List<Payments>> snapshot) {
                        switch (snapshot.connectionState) {
                          case ConnectionState.none:
                          case ConnectionState.waiting:
                            return const CircularProgressIndicator();
                          case ConnectionState.active:
                          case ConnectionState.done:
                            if (snapshot.hasError) {
                              return Text('Error: ${snapshot.error}');
                            } else {
                              return RefreshIndicator(
                                key: _refreshIndicatorKey,
                                color: Colors.blue,
                                onRefresh: () {
                                  setState(() {});
                                  return userPayments = getUserPayments();
                                },
                                child: snapshot.data!.length > 0
                                    ? GroupedListView<Payments, String?>(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 18.w, vertical: 30.h),
                                        groupBy: (element) => DateTime.parse(
                                                element.startDate ??
                                                    '0000-00-00')
                                            .year
                                            .toString(),
                                        order: GroupedListOrder.DESC,
                                        elements: snapshot.data ?? [],
                                        groupSeparatorBuilder:
                                            (String? value) => Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Align(
                                            alignment: Alignment.center,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          25.r),
                                                  color: cBlackColor
                                                      .withAlpha(20)),
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 10.w,
                                                  vertical: 5.h),
                                              child: Text(
                                                '$value-йил',
                                                style: TextStyle(
                                                    color: cBlackColor,
                                                    fontSize: 18.sp,
                                                    fontFamily: 'Medium'),
                                              ),
                                            ),
                                          ),
                                        ),
                                        itemBuilder: (ctx, element) {
                                          return HistoryItem(
                                              paymentModel: element);
                                        },
                                        physics: AlwaysScrollableScrollPhysics(
                                            parent: BouncingScrollPhysics()),
                                      )
                                    : Column(
                                        children: [
                                          SizedBox(
                                            height: 80.h,
                                          ),
                                          Image.asset(
                                            Assets.iconsEmpty,
                                            height: 300.h,
                                          ),
                                          SizedBox(
                                            height: 20.h,
                                          ),
                                          MaterialButton(
                                            onPressed: () {
                                              setState(() {
                                                userPayments =
                                                    getUserPayments();
                                              });
                                            },
                                            child: Text('Янгилаш'),
                                            color: cFirstColor,
                                            elevation: 0,
                                            minWidth: 360.w,
                                            height: 70.h,
                                            textColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        cRadius16.r)),
                                          )
                                        ],
                                      ),
                              );
                            }
                        }
                      }),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<List<Payments>> getUserPayments() async {
  final Dio dio = di();
  SharedPreferences prefs = di();
  List<Payments> payments = [];

  var sector = '';
  var region = '';
  var province = '';

  try {
    final response = await dio.get(baseUrl + paymentInfoPath,
        options: Options(headers: <String, String>{
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + '${prefs.getString('token')}'
        }));
    final data = response.data['payments'];
    print(data.toString());
    if (response.statusCode == 200) {
      for (int i = 0; i < (data.length); i++) {
        payments.add(Payments.fromJson(data[i]));
      }
      return payments;
    } else {
      CustomToast.showToast(data.toString());
      print(data.toString());
      return [];
    }
  } on DioException catch (e) {
    if (e.type == DioExceptionType.badResponse) {
      if (e.response != null) {
        CustomToast.showToast('Server error: ' + "${e.response!.data}");
        print('Server error: ' + "${e.response!.data}");
      }
    }
    return [];
  }
}
