class PaymentRes {
  PaymentRes({
      this.status, 
      this.text, 
      this.date,});

  PaymentRes.fromJson(dynamic json) {
    status = json['status'];
    text = json['text'];
    date = json['date'];
  }
  bool? status;
  String? text;
  int? date;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['status'] = status;
    map['text'] = text;
    map['date'] = date;
    return map;
  }

}