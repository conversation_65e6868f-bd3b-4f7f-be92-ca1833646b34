import 'package:dio/dio.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/features/profile/data/models/payment_info_model.dart';
import 'package:etahlil/features/profile/data/models/tariff_model.dart';
import 'package:etahlil/features/profile/presentation/widgets/expansion_item.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/profile/presentation/pages/payments/web_view.dart';
import 'package:etahlil/generated/assets.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PaymentsPage extends StatefulWidget {
  const PaymentsPage({Key? key}) : super(key: key);

  @override
  _PaymentsPageState createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> {
  SharedPreferences prefs = di();
  TariffModel? _groupValueTariff;
  late Future<List<TariffModel>> tariffs;

  checkNetwork() async {
    NetworkInfo networkInfo = di();

    if (!await networkInfo.isConnected) {
      CustomToast.showToast('Интернет алоқасини текширинг!');
    }
  }

  @override
  void initState() {
    checkNetwork();
    tariffs = getTariffs();
    tariffs.then((value) {
      setState(() {
        _groupValueTariff = value.first;
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: cFirstColor,
      child: SafeArea(
        maintainBottomViewPadding: true,
        minimum: EdgeInsets.zero,
        child: Scaffold(
          backgroundColor: cBackColor,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
                decoration: BoxDecoration(
                    gradient: cFirstGradient,
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(cRadius22.r),
                        bottomRight: Radius.circular(cRadius22.r))),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: SvgPicture.asset(
                          "assets/icons/arrow_left.svg",
                          width: 24.w,
                          height: 24.h,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text("Тўлов қилиш",
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        style: TextStyle(
                            fontSize: 18.sp,
                            color: cWhiteColor,
                            fontFamily: 'Medium')),
                    const Spacer(),
                    SizedBox(
                      width: 20.w,
                    )
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 30.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Тарифлар",
                        style: TextStyle(
                            fontSize: 20.sp,
                            color: cBlackColor,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Medium')),
                    SizedBox(
                      height: 10.h,
                    ),
                    ExpansionItem(
                        title:
                            '${_groupValueTariff?.term ?? '0'} ойга — ${_groupValueTariff?.price ?? '0'} сўм',
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 10.h),
                          child: FutureBuilder<List<TariffModel>>(
                            future: tariffs, // async work
                            builder: (BuildContext context,
                                AsyncSnapshot<List<TariffModel>> snapshot) {
                              switch (snapshot.connectionState) {
                                // case ConnectionState.waiting:
                                //   return Text('Loading....');
                                default:
                                  if (snapshot.hasError)
                                    return Text('Error: ${snapshot.error}');
                                  else
                                    return Column(
                                      children: snapshot.data
                                              ?.map((TariffModel value) {
                                            return RadioListTile(
                                              title: Text(
                                                '${value.term} ойга — ${value.price} сўм',
                                                style: TextStyle(
                                                    color: cBlackColor),
                                              ),
                                              value: value,
                                              groupValue: _groupValueTariff,
                                              onChanged: (newValue) => setState(
                                                  () => _groupValueTariff =
                                                      newValue as TariffModel),
                                            );
                                          }).toList() ??
                                          [],
                                    );
                              }
                            },
                          ),
                        )),
                    SizedBox(
                      height: 25.h,
                    ),
                    Text("Тўлов тизими",
                        style: TextStyle(
                            fontSize: 20.sp,
                            color: cBlackColor,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Medium')),
                    SizedBox(
                      height: 10.h,
                    ),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: InkWell(
                            onTap: () async {
                              var price = _groupValueTariff?.price ?? 0;
                              int userId = await getUserId() ?? -1;
                              if (price > 500 && userId != -1) {
                                Navigator.push(
                                  context,
                                  CupertinoPageRoute(
                                      builder: (context) => WebViewPage(
                                            amount: price,
                                            userId: userId,
                                          )),
                                );
                              } else {
                                CustomToast.showToast('Тўлов қилиш имконсиз!');
                              }
                            },
                            child: Card(
                              elevation: 1,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20)),
                              child: Center(
                                child: Container(
                                    height: 90.h,
                                    // width: 200.w,
                                    child: Image.asset(
                                      Assets.imagesClickLogo,
                                      width: 120.w,
                                      fit: BoxFit.contain,
                                    )),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: InkWell(
                            onTap: () {
                              CustomToast.showToast(
                                  "Ушбу тўлов тизими қўшилиш жараёнида!");
                            },
                            child: Card(
                              elevation: 1,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20)),
                              child: Center(
                                child: Container(
                                    height: 90.h,
                                    // width: 200.w,
                                    child: Image.asset(
                                      Assets.imagesPaymeLogo,
                                      width: 100.w,
                                      fit: BoxFit.contain,
                                    )),
                              ),
                            ),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<int?> getUserId() async {
    final Dio dio = di();
    SharedPreferences prefs = di();
    var userId = prefs.getString('id') ?? '';

    try {
      final response = await dio.get(baseUrl + usersPath + userId,
          options: Options(headers: <String, String>{
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + '${prefs.getString('token')}'
          }));
      final data = response.data;
      print(data.toString());
      if (response.statusCode == 200) {
        var payInfo = PaymentInfoModel.fromJson(data);
        return payInfo.paymentID;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return null;
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return null;
    }
  }

  Future<List<TariffModel>> getTariffs() async {
    final Dio dio = di();

    List<TariffModel> tariffs = [];

    try {
      final response = await dio.get(baseUrl + paymentPlans,
          options: Options(headers: <String, String>{
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + '${prefs.getString('token')}'
          }));
      final data = response.data;
      print(data.toString());
      if (response.statusCode == 200) {
        for (int i = 0; i < (data.length); i++) {
          tariffs.add(TariffModel.fromJson(data[i]));
        }
        return tariffs;
      } else {
        CustomToast.showToast(data.toString());
        print(data.toString());
        return [];
      }
    }on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          CustomToast.showToast('Server error: ' + "${e.response!.data}");
          print('Server error: ' + "${e.response!.data}");
        }
      }
      return [];
    }
  }
}
