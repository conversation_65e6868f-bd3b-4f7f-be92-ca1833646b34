import 'dart:ui';

import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/profile/data/models/prof_model.dart';
import 'package:etahlil/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:etahlil/features/profile/presentation/widgets/info_widget.dart';
import 'package:etahlil/generated/assets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../login/presentation/pages/login_page.dart';
import '../../../password/presentation/pages/password_dialog.dart';
import 'package:http/http.dart' as http;

class Profile extends StatefulWidget {
  const Profile({Key? key}) : super(key: key);

  static Widget screen() {
    return BlocProvider(
      create: (context) => di<ProfileBloc>(),
      child: const Profile(),
    );
  }

  @override
  _ProfileState createState() => _ProfileState();
}

class _ProfileState extends State<Profile> {
  late ProfileBloc _bloc;
  SharedPreferences prefs = di();

  @override
  void initState() {
    _bloc = BlocProvider.of<ProfileBloc>(context);

    var date = prefs.getString("dateProf");
    _handleRefresh(date != null ? false : true);

    super.initState();
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  Future _handleRefresh(bool refresh) async {
    _bloc.add(GetDataProfile(refresh: refresh));
  }

  Future _onlineRefresh() async {
    _bloc.add(GetDataProfile(refresh: true));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: cFirstColor,
      child: SafeArea(
        maintainBottomViewPadding: true,
        minimum: EdgeInsets.zero,
        child: Scaffold(
          backgroundColor: cBackColor,
          body: BlocBuilder<ProfileBloc, ProfileState>(
            builder: (context, state) {
              if (state is ProfileFailure) {
                CustomToast.showToast(
                    "Маълумотлар юкланишда хатолик юз берди!");
              }
              if (state is ProfileLoading) {
                return const Center(child: CupertinoActivityIndicator());
              } else if (state is ProfileSuccess) {
                var date = prefs.getString("dateProf") ?? "00-00-0000 00.00.00";

                return RefreshIndicator(
                  onRefresh: _onlineRefresh,
                  child: ListView(
                    children: [
                      Container(

                        ///Adjusting to the Page size
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(vertical: 5.h),
                              width: double.infinity,
                              child: Text("Охирги янгиланиш: $date",
                                  style: TextStyle(color: cWhiteColor)),
                              alignment: Alignment.bottomCenter,
                              decoration: BoxDecoration(color: Colors.green),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 25.w, vertical: 30.h),
                              decoration: BoxDecoration(
                                  color: cFirstColor,
                                  borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(cRadius22.r),
                                      bottomRight:
                                      Radius.circular(cRadius22.r))),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 24.w,
                                  ),
                                  const Spacer(),
                                  SizedBox(
                                    child: Text("Фойдаланувчи маълумоти",
                                        textAlign: TextAlign.center,
                                        maxLines: 2,
                                        style: TextStyle(
                                            fontSize: 18.sp,
                                            color: cWhiteColor,
                                            fontFamily: 'Medium')),
                                    width: 300.w,
                                  ),
                                  const Spacer(),
                                  InkWell(
                                    onTap: () =>
                                        showDialog(
                                          context: context,
                                          builder: (_) =>
                                              AlertDialog(
                                                title: const Text(
                                                    'Дастурдан чиқишни ҳоҳлайсизми?'),
                                                content: const Text(
                                                    'Сизнинг барча шахсий маълумотларингиз қурилмангиздан ўчириб юборилади!'),
                                                actions: <Widget>[
                                                  MaterialButton(
                                                    elevation: 0,
                                                    color: Colors.green.shade50,
                                                    onPressed: () {
                                                      Navigator.of(context).pop(
                                                          false);
                                                    },
                                                    child: const Text(
                                                      'Йўқ',
                                                      style: TextStyle(
                                                          color: Colors.green),
                                                    ),
                                                  ),
                                                  MaterialButton(
                                                    elevation: 0,
                                                    color: Colors.red.shade50,
                                                    onPressed: () async {
                                                      clearAndLogout();
                                                    },
                                                    child: const Text(
                                                      'Ҳа',
                                                      style:
                                                      TextStyle(
                                                          color: Colors.red),
                                                    ),
                                                  ),
                                                ],
                                                actionsPadding:
                                                const EdgeInsets.all(10),
                                              ),
                                        ),
                                    child: Container(
                                      height: 30.h,
                                      width: 30.w,
                                      child: SvgPicture.asset(
                                        "assets/icons/logout.svg",
                                        color: cWhiteColor,
                                        height: 24.h,
                                        width: 24.w,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 12.h,
                            ),
                            Text(
                              state.object.regionName ?? 'region',
                              style: TextStyle(
                                  color: cGrayColor2,
                                  fontSize: 14.sp,
                                  fontFamily: 'Medium'),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: Text(
                                state.object.name ?? 'name',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    color: cGrayColor4,
                                    fontSize: 24.sp,
                                    fontFamily: 'Medium'),
                              ),
                            ),
                            SizedBox(
                              height: 4.h,
                            ),
                            Text(
                              "+998" + state.object.phoneNumber.toString(),
                              style: TextStyle(
                                  color: cFirstColor,
                                  fontSize: 16.sp,
                                  fontFamily: 'Medium'),
                            ),
                            SizedBox(
                              height: 12.h,
                            ),
                            Image.asset(
                              "assets/icons/person.png",
                              width: 153.w,
                              height: 153.h,
                            ),
                            SizedBox(
                              height: 12.h,
                            ),
                            Card(
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 15.h,
                                  ),
                                  infoWidget(
                                    state.object.viloyatBoyicha.toString(),
                                    "Вилоятдаги барча секторлар ичида",
                                    state.object.tumanBoyicha.toString(),
                                    "Туман (Шаҳар)нинг 4 та сектори ичида",
                                    cFirstColor,
                                    cFirstColor,
                                  ),
                                  SizedBox(
                                    height: 15.h,
                                  ),
                                  infoWidget(
                                      state.object.barchaJonatmalar.toString(),
                                      "Барча юборилган маьлумотлар сони",
                                      state.object.sectorKotibiKormoqda
                                          .toString(),
                                      "Сектор котиби кўриб чиқмоқда",
                                      cYellowColor,
                                      cYellowColor),
                                  SizedBox(
                                    height: 15.h,
                                  ),
                                  infoWidget(
                                      state.object.rejaGrafikBoyicha.toString(),
                                      "Режа графиги бўйича жами",
                                      state.object.bajarilishiKerakIshlar
                                          .toString(),
                                      "Бажарилиши керак бўлган ишлар",
                                      cYellowColor,
                                      cRedColor),
                                  SizedBox(
                                    height: 15.h,
                                  ),
                                ],
                              ),
                              margin: EdgeInsets.symmetric(horizontal: 21.w),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                BorderRadius.circular(cRadius22.r),
                              ),
                            ),
                            SizedBox(
                              height: 12.h,
                            ),
                            GestureDetector(
                              onTap: () {
                                showDialog(
                                    context: context,
                                    builder: (_) =>
                                        BackdropFilter(
                                          filter: ImageFilter.blur(
                                              sigmaX: 5, sigmaY: 5),
                                          child: AlertDialog(
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                BorderRadius.circular(
                                                    20.r)),
                                            backgroundColor: cWhiteColor,
                                            title: Text(
                                                "Аккаунтингизни ўчиришни ҳохлайсизми?"),
                                            content: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                      "Сизнинг шахсий маълумотларингиз қурилмангиздан ўчириб юборилади.."),
                                                  SizedBox(
                                                    height: 10.h,
                                                  ),
                                                  Text(
                                                    "Аккаунтни қайтариб бўлмайди",
                                                    style: TextStyle(
                                                        color: cRedColor),
                                                  ),
                                                ]),
                                            actions: <Widget>[
                                              MaterialButton(
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                    BorderRadius.all(
                                                        Radius.circular(
                                                            10))),
                                                elevation: 0,
                                                color: Colors.green.shade50,
                                                onPressed: () {
                                                  Navigator.of(context)
                                                      .pop(false);
                                                },
                                                child: Text(
                                                  "Йўқ",
                                                  style: TextStyle(
                                                      color: Colors.green),
                                                ),
                                              ),
                                              MaterialButton(
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                    BorderRadius.all(
                                                        Radius.circular(
                                                            10))),
                                                elevation: 0,
                                                color: Colors.red.shade50,
                                                onPressed: () async {
                                                  ///Here is logging out and deletion!
                                                  var isDeleted =
                                                  await deleteUser();
                                                  if (isDeleted) {
                                                    clearAndLogout();
                                                    CustomToast.showToast(
                                                        "Ҳисобингиз бутунлай ўчирилди!");
                                                  }
                                                },
                                                child: Text(
                                                  "Ўчириш",
                                                  style: TextStyle(
                                                      color: Colors.red),
                                                ),
                                              ),
                                            ],
                                            actionsPadding:
                                            const EdgeInsets.all(10),
                                          ),
                                        ));
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 7.5),
                                decoration: BoxDecoration(
                                    boxShadow: [
                                      BoxShadow(
                                          color: Colors.grey.withOpacity(0.1),
                                          spreadRadius: 10,
                                          blurRadius: 20,
                                          blurStyle: BlurStyle.normal),
                                    ],
                                    borderRadius: BorderRadius.circular(15.r),
                                    color: cRedColor.withAlpha(40)),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 15.w, vertical: 12.h),
                                child: Row(
                                  children: [
                                    SvgPicture.asset(
                                      Assets.iconsDelete,
                                      color: cRedColor,
                                      height: 20.h,
                                      width: 20.w,
                                    ),
                                    const SizedBox(width: 18),
                                    Expanded(
                                      child: Text(
                                        "Аккаунтни ўчириш (Диққат!)",
                                        style: TextStyle(
                                            color: cRedColor,
                                            fontSize: 16.sp,
                                            fontFamily: 'Medium'),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                showDialog(
                                    context: context,
                                    builder: (context) {
                                      return PasswordEditDialog.screen();
                                    }).then((value) {
                                  setState(() {});
                                });
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(horizontal: 21.w),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15.r),
                                    color: cWhiteColor),
                                padding: EdgeInsets.all(20.h),
                                child: Row(
                                  children: [
                                    SvgPicture.asset(
                                        "assets/icons/ic_shield-security.svg",
                                        width: 24.w,
                                        height: 24.h,
                                        color: cFirstColor),
                                    SizedBox(width: 18.w),
                                    Expanded(
                                      child: Text(
                                        "Паролни ўрнатиш (ўзгартириш)",
                                        style: TextStyle(
                                            color: cFirstColor,
                                            fontSize: 16.sp,
                                            fontFamily: 'Regular'),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 30.h,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  "assets/icons/corp.svg",
                                  height: 10.h,
                                  width: 10.w,
                                ),
                                SizedBox(
                                  width: 8.w,
                                ),
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "Вилоят электрон ҳокимиятни ривожлантириш маркази",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          fontSize: 10.sp,
                                          fontFamily: "Medium",
                                          color: cGrayColor),
                                    ),
                                    SizedBox(
                                      height: 5.h,
                                    ),
                                    Text(
                                      "v: " + version,
                                      style: TextStyle(
                                          fontSize: 10.sp,
                                          fontFamily: "Medium",
                                          color: cGrayColor),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 15.h,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                );
              } else {
                return Container();
              }
            },
          ),
        ),
      ),
    );
  }

  Future<bool> deleteUser() async {
    final NetworkInfo networkInfo = di();
    late http.Client client = di();

    String? userId = prefs.getString("id") ?? "";
    if (await networkInfo.isConnected) {
      try {
        var response = await client.delete(
          Uri.parse(baseUrl + usersPath + userId),
          headers: <String, String>{
            'Content-Type': 'application/json; charset=UTF-8',
            'Accept': 'application/json',
            "Authorization": "Bearer ${prefs.getString("token")}"
          },
        );
        if (response.statusCode == 200) {
          print("*** Account Deleted: " + response.body);
          return true;
        } else {
          return false;
        }
      } catch (e) {
        print(e);
        return false;
      }
    } else {
      CustomToast.showToast("Интернетга уланишда муаммо бор!");
      return false;
    }
  }

  clearAndLogout() async {
    SharedPreferences prefs = di.get();

    try {
      await prefs.remove('id');
      await prefs.remove('name');
      await prefs.remove('login');
      await prefs.remove('phone');
      await prefs.remove('regionId');
      await prefs.remove('sectorId');
      await prefs.remove('pin_code');
      await prefs.remove('token');
      await prefs.remove('isDemo');
      await prefs.remove(BASE_URL);

      await prefs.remove('dateHome');
      await prefs.remove('dateNew');
      await prefs.remove('dateOld');
      await prefs.remove('dateProf');

      Hive.box(profileBox).clear();
      Hive.box(categoryBox).clear();
      Hive.box(subCategoryBox).clear();
      Hive.box(newHistoryBox).clear();
      Hive.box(oldHistoryBox).clear();
      // Hive.box(forSendBox).clear();
    } catch (e) {}

    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (BuildContext context) => LoginPage.screen(),
      ),
          (Route route) => false,
    );
  }
}
