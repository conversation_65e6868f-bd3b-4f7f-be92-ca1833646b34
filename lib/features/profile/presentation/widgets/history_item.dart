import 'package:etahlil/features/profile/data/models/payment_info_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../../core/utils/app_constants.dart';

class HistoryItem extends StatelessWidget {
  final Payments paymentModel;

  HistoryItem({super.key, required this.paymentModel});

  final DateFormat formatterDate = DateFormat('dd.MM.yyyy');

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.h, vertical: 7.5.w),
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 10,
            blurRadius: 20,
            blurStyle: BlurStyle.normal),
      ], borderRadius: BorderRadius.circular(25.r), color: cWhiteColor),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    "Таьриф",
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25.r),
                        color: cSecondColor.withAlpha(50)),
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                    child: Text(
                      ' ${paymentModel.month ?? "0"} ойлик',
                      style: TextStyle(
                          color: cFirstColor,
                          fontSize: 16.sp,
                          fontFamily: 'Medium'),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    "Сумма",
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Text(
                    paymentModel.amount ?? '0',
                    style: TextStyle(
                        color: cBlackColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    "Бошланган вақти",
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Text(
                    formatterDate.format(
                        DateTime.parse(paymentModel.startDate ?? '0000-00-00')),
                    style: TextStyle(
                        color: cBlackColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 5.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    "Якунланган вақти",
                    style: TextStyle(
                        color: cGrayColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
                SizedBox(width: 18.h),
                Flexible(
                  child: Text(
                    formatterDate.format(
                        DateTime.parse(paymentModel.endDate ?? '0000-00-00')),
                    style: TextStyle(
                        color: cBlackColor,
                        fontSize: 16.sp,
                        fontFamily: 'Medium'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
