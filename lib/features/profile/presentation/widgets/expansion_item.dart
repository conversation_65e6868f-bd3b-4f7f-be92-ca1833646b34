import 'package:etahlil/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ExpansionItem extends StatefulWidget {
  final String title;
  final Widget child;

  const ExpansionItem({Key? key, required this.title, required this.child})
      : super(key: key);

  @override
  State<ExpansionItem> createState() => _ExpansionItemState();
}

class _ExpansionItemState extends State<ExpansionItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 2.h),
      decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(6),
              blurRadius: 8.0,
              spreadRadius: 8.0,
              // offset: Offset(2.0, 2.0), // shadow direction: bottom right
            )
          ],
          color: cWhiteColor, borderRadius: BorderRadius.circular(20.r)),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          tilePadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 2.h),
          title: Text(widget.title),
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: widget.child,
            ),
          ],
        ),
      ),
    );
  }
}
