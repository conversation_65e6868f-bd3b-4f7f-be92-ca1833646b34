class TariffModel {
  TariffModel({
    this.id,
    this.price,
    this.term,
    this.createdAt,
    this.updatedAt,});

  TariffModel.fromJson(dynamic json) {
    id = json['_id'];
    price = json['price'];
    term = json['term'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
  String? id;
  int? price;
  int? term;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['price'] = price;
    map['term'] = term;
    map['createdAt'] = createdAt;
    map['updatedAt'] = updatedAt;
    return map;
  }

}