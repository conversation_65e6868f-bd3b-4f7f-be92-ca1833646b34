class PaymentInfoModel {
  PaymentInfoModel({
      this.id, 
      this.title, 
      this.region, 
      this.province, 
      this.sector, 
      this.endDate, 
      this.payments, 
      this.paymentID, 
      this.contract,});

  PaymentInfoModel.fromJson(dynamic json) {
    id = json['_id'];
    title = json['title'];
    region = json['region'];
    province = json['province'];
    sector = json['sector'];
    endDate = json['endDate'];
    if (json['payments'] != null) {
      payments = [];
      json['payments'].forEach((v) {
        payments?.add(Payments.fromJson(v));
      });
    }
    paymentID = json['paymentID'];
    if (json['contract'] != null) {
      contract = [];
      json['contract'].forEach((v) {
        contract?.add(Contract.fromJson(v));
      });
    }
  }
  String? id;
  String? title;
  String? region;
  String? province;
  String? sector;
  String? endDate;
  List<Payments>? payments;
  int? paymentID;
  List<Contract>? contract;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['_id'] = id;
    map['title'] = title;
    map['region'] = region;
    map['province'] = province;
    map['sector'] = sector;
    map['endDate'] = endDate;
    if (payments != null) {
      map['payments'] = payments?.map((v) => v.toJson()).toList();
    }
    map['paymentID'] = paymentID;
    if (contract != null) {
      map['contract'] = contract?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class Contract {
  Contract({
      this.cantract, 
      this.amount, 
      this.month, 
      this.startDate, 
      this.endDate,});

  Contract.fromJson(dynamic json) {
    cantract = json['cantract'];
    amount = json['amount'];
    month = json['month'];
    startDate = json['startDate'];
    endDate = json['endDate'];
  }
  String? cantract;
  int? amount;
  int? month;
  String? startDate;
  String? endDate;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['cantract'] = cantract;
    map['amount'] = amount;
    map['month'] = month;
    map['startDate'] = startDate;
    map['endDate'] = endDate;
    return map;
  }

}

class Payments {
  Payments({
      this.clickTransId, 
      this.amount, 
      this.signTime, 
      this.error, 
      this.errorNote, 
      this.month, 
      this.startDate, 
      this.endDate,});

  Payments.fromJson(dynamic json) {
    clickTransId = json['click_trans_id'];
    amount = json['amount'];
    signTime = json['sign_time'];
    error = json['error'];
    errorNote = json['error_note'];
    month = json['month'];
    startDate = json['startDate'];
    endDate = json['endDate'];
  }
  String? clickTransId;
  String? amount;
  String? signTime;
  String? error;
  String? errorNote;
  int? month;
  String? startDate;
  String? endDate;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['click_trans_id'] = clickTransId;
    map['amount'] = amount;
    map['sign_time'] = signTime;
    map['error'] = error;
    map['error_note'] = errorNote;
    map['month'] = month;
    map['startDate'] = startDate;
    map['endDate'] = endDate;
    return map;
  }

}