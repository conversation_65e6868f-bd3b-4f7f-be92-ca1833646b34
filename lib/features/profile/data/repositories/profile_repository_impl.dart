import 'package:dartz/dartz.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/network/network_info.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/demo/demo_data.dart';
import 'package:etahlil/features/profile/data/datasources/profile_local_datasources.dart';
import 'package:etahlil/features/profile/data/datasources/profile_remote_datasources.dart';
import 'package:etahlil/features/profile/data/models/prof_model.dart';
import 'package:etahlil/features/profile/domain/repositories/profile_repository.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfRepositoryImpl extends ProfRepository {
  final ProfRemoteDatasourceImpl profRemoteDatasource;
  final ProfileLocalDataSourcesImpl profLocalDatasource;
  final NetworkInfo networkInfo;

  ProfRepositoryImpl(
      {required this.profRemoteDatasource,
      required this.profLocalDatasource,
      required this.networkInfo});

  @override
  Future<Either<Failure, dynamic>> getProf(bool refresh) async {
    SharedPreferences sharedPreferences = di();
    bool isDemo = sharedPreferences.getBool("isDemo") ?? false;
    var time = (DateFormat('dd-MM-yyyy  HH:mm:ss').format(DateTime.now()));

    if (!isDemo) {
      if (await networkInfo.isConnected && refresh) {
        try {
          final result = await profRemoteDatasource.getData();
          profLocalDatasource.setProf(result);

          sharedPreferences.setString("dateProf", time);

          return Right(result);
        } on ServerFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      } else {
        try {
          final result = await profLocalDatasource.getProf();
          if (result == null)
            return Right(ProfModel());
          else
            return Right(result);
        } on LocalFailure {
          return const Left(ServerFailure("Маълумот юкланишда хатолик бўлди"));
        }
      }
    } else {
      return Right(getProfDemo());
    }
  }
}
