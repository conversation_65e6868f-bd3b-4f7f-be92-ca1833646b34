import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/features/profile/data/models/prof_model.dart';
import 'package:hive/hive.dart';

abstract class ProfileLocalDataSources {
  Future<dynamic> getProf();

  Future<bool> setProf(ProfModel list);
}

class ProfileLocalDataSourcesImpl extends ProfileLocalDataSources {
  @override
  Future<dynamic> getProf() async {
    final box = Hive.box(profileBox);
    //It may return null
    return box.get(profileBox);
  }

  @override
  Future<bool> setProf(ProfModel user) async {
    try {
      final box = Hive.box(profileBox);
      box.put(profileBox, user);
      return true;
    } catch (e) {
      return false;
    }
  }
}
