import 'dart:convert';

import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/utils/api_path.dart';
import 'package:etahlil/features/profile/data/models/prof_model.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

abstract class ProfRemoteDatasource {
  Future<dynamic> getData();
}

class ProfRemoteDatasourceImpl implements ProfRemoteDatasource {
  final SharedPreferences sharedPreferences;
  final http.Client client;

  ProfRemoteDatasourceImpl(
      {required this.sharedPreferences, required this.client});

  @override
  Future<dynamic> getData() async {
    ProfModel? userData;
    String? userId = sharedPreferences.getString("id") ?? "";
    String? regionId = sharedPreferences.getString("regionId") ?? "";
    String? sectorId = sharedPreferences.getString("sectorId") ?? "";
    String? name = sharedPreferences.getString("name") ?? "";
    String? phone = sharedPreferences.getString("phone") ?? "";

    var now = DateTime.now();
    var time = (DateFormat('yyyy-MM-dd').format(now));

    try {
      final response = await client.post(Uri.parse(baseUrl + doneWorkPath),
          headers: <String, String>{
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            "Authorization": "Bearer ${sharedPreferences.getString("token")}"
          },
          body: jsonEncode(<String, String>{
            'date': time,
            'region': regionId,
            'sector': sectorId
          }));

      final responseReport = await client.post(Uri.parse(baseUrl + reportPath),
          headers: <String, String>{
            'Content-Type': 'application/json; charset=UTF-8',
            'Accept': 'application/json',
            "Authorization": "Bearer ${sharedPreferences.getString("token")}"
          },
          body: jsonEncode(<String, String>{'date': time}));

      final responsePlan = await client.post(Uri.parse(baseUrl + plansPath),
          headers: <String, String>{
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            "Authorization": "Bearer ${sharedPreferences.getString("token")}"
          },
          body: jsonEncode(<String, String>{'user': userId, 'date': time}));

      final responseReg = await client.get(
        Uri.parse(baseUrl + "/regions/" + regionId),
        headers: <String, String>{
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
      );

      if (response.statusCode == 200 &&
          responsePlan.statusCode == 200 &&
          responseReg.statusCode == 200 &&
          responseReport.statusCode == 200) {
        dynamic parsed;
        dynamic parsedPlan;
        dynamic parsedReg;
        dynamic parsedReport;

        try {
          parsed = json.decode(response.body);
          parsedReg = json.decode(responseReg.body);
          parsedPlan = json.decode(responsePlan.body);
          parsedReport = json.decode(responseReport.body);
        } catch (e) {
          debugPrint(e.toString());
        }

        var regionReport = parsedReport["urin"] ?? [] as Map;
        var sectorReport = parsedReport["urin"] ?? [] as Map;
        int? regionRate = 0;
        int? sectorRate = 0;

        if (regionReport.isNotEmpty)
          regionRate = regionReport[0]?['region'] ?? 0;
        if (sectorReport.isNotEmpty)
          sectorRate = sectorReport[1]?['sector'] ?? 0;

        var sendCount = parsed["sendWorks"]?["count"] ?? 0;
        var newCount = parsed["newWorks"]?["count"] ?? 0;
        var plan = parsedPlan["plan"] ?? 0;
        var notDone = parsedPlan["notDone"] ?? 0;
        var region = parsedReg["title"] ?? "...";
        var doneCount = sendCount + newCount;

        userData = ProfModel(
          id: userId,
          name: name,
          regionName: region,
          sectorId: sectorId,
          phoneNumber: phone,
          viloyatBoyicha: regionRate,
          tumanBoyicha: sectorRate,
          barchaJonatmalar: doneCount,
          sectorKotibiKormoqda: newCount,
          rejaGrafikBoyicha: plan,
          bajarilishiKerakIshlar: notDone,
        );
        return userData;
      } else {
        return ProfModel(
          id: "",
          name: name,
          regionName: "",
          sectorId: "",
          phoneNumber: phone,
          viloyatBoyicha: 0,
          tumanBoyicha: 0,
          barchaJonatmalar: 0,
          sectorKotibiKormoqda: 0,
          rejaGrafikBoyicha: 0,
          bajarilishiKerakIshlar: 0,
        );
      }
    } on InputFormatterFailure {
      return "500";
    }
  }
}
