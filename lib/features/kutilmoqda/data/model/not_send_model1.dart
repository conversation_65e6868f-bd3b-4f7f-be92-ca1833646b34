import 'package:etahlil/features/send_data/data/models/img_model.dart';
import 'package:hive/hive.dart';

part 'not_send_model1.g.dart';

@HiveType(typeId: 5)
class NotSendModel extends HiveObject {
  @HiveField(0)
  String? userId;
  @HiveField(1)
  String? categoryId;
  @HiveField(2)
  String? subCategoryId;
  @HiveField(3)
  String? orinbosarIshtirokida;
  @HiveField(4)
  String? cause;
  @HiveField(5)
  String? title;
  @HiveField(6)
  String? text;
  @HiveField(7)
  List<ImgModel>? imagesList;
  @HiveField(8)
  String? categoryName;
  @HiveField(9)
  String? subCategoryName;

  NotSendModel(
      {this.userId,
      this.categoryId,
      this.subCategoryId,
      this.orinbosarIshtirokida,
      this.cause,
      this.title,
      this.text,
      this.categoryName,
      this.subCategoryName,
      this.imagesList});
}
