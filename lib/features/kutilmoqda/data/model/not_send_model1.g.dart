// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'not_send_model1.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NotSendModelAdapter extends TypeAdapter<NotSendModel> {
  @override
  final int typeId = 5;

  @override
  NotSendModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NotSendModel(
      userId: fields[0] as String?,
      categoryId: fields[1] as String?,
      subCategoryId: fields[2] as String?,
      orinbosarIshtirokida: fields[3] as String?,
      cause: fields[4] as String?,
      title: fields[5] as String?,
      text: fields[6] as String?,
      categoryName: fields[8] as String?,
      subCategoryName: fields[9] as String?,
      imagesList: (fields[7] as List?)?.cast<ImgModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, NotSendModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.categoryId)
      ..writeByte(2)
      ..write(obj.subCategoryId)
      ..writeByte(3)
      ..write(obj.orinbosarIshtirokida)
      ..writeByte(4)
      ..write(obj.cause)
      ..writeByte(5)
      ..write(obj.title)
      ..writeByte(6)
      ..write(obj.text)
      ..writeByte(7)
      ..write(obj.imagesList)
      ..writeByte(8)
      ..write(obj.categoryName)
      ..writeByte(9)
      ..write(obj.subCategoryName);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotSendModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
