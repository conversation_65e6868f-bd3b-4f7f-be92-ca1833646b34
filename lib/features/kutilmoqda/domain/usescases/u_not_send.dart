import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:etahlil/core/errors/failures.dart';
import 'package:etahlil/core/usescases/usecase.dart';

import '../../data/model/not_send_model1.dart';
import '../repository/yuborilmagan_repository.dart';

class NotSend extends UseCase<dynamic, NotSendParams> {
  final NotSendRepository notSendRepository;

  NotSend({required this.notSendRepository});

  @override
  Future<Either<Failure, dynamic>> call(NotSendParams params) {
    return notSendRepository.setNotSends(params.notSendModel);
  }
}

class NotSendParams extends Equatable {
  final NotSendModel notSendModel;

  @override
  List<Object?> get props => [notSendModel];

  const NotSendParams(this.notSendModel);
}
