import 'dart:convert';

import 'package:aescryptojs/aescryptojs.dart';
import 'package:dio/dio.dart';
import 'package:etahlil/core/functions.dart';
import 'package:etahlil/core/utils/app_constants.dart';
import 'package:etahlil/core/widgets/alert_dialog.dart';
import 'package:etahlil/core/widgets/custum_toast.dart';
import 'package:etahlil/di/dependency_injection.dart';
import 'package:etahlil/features/kutilmoqda/presentetion/bloc/not_send_bloc.dart';
import 'package:etahlil/features/profile/presentation/pages/payments/payment_response.dart';
import 'package:etahlil/features/profile/presentation/pages/payments/payments.dart';
import 'package:etahlil/generated/assets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive/hive.dart';
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sn_progress_dialog/progress_dialog.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import '../../../../core/network/network_info.dart';
import '../../../../core/utils/api_path.dart';
import '../../data/model/not_send_model1.dart';

class NotSendPage extends StatefulWidget {
  const NotSendPage({Key? key}) : super(key: key);

  static Widget screen() => BlocProvider(
        create: (context) => di<NotSendBloc>()..add(GetNotSendEvent()),
        child: const NotSendPage(),
      );

  @override
  _NotSendPageState createState() => _NotSendPageState();
}

class _NotSendPageState extends State<NotSendPage> {
  late NotSendBloc _bloc;
  final NetworkInfo networkInfo = di.get();
  SharedPreferences sharedPreferences = di.get();
  Dio dio = di.get();
  late ProgressDialog pd;
  int selectedIndex = -1;
  List data = [];

  late Future<dynamic> _paymentStatusInit;

  Future<dynamic> checkUserPayment() async {
    if (await networkInfo.isConnected) {
      final Dio dio = di();
      SharedPreferences prefs = di();

      var provinceId = prefs.getString('provinceId') ?? '';
      var regionId = prefs.getString('regionId') ?? '';
      var sectorId = prefs.getString('sectorId') ?? '';

      ///

      try {
        final response = await dio.post(controlUrl + payCheckPath,
            options: Options(
                headers: <String, String>{'Content-Type': 'application/json'}),
            data: {
              "province": provinceId,
              "region": regionId,
              "sector": sectorId,
            });
        final data = response.data['data'];
        print(data.toString());
        if (response.statusCode == 200) {
          String privateKey = DateTime.now().toIso8601String().substring(0, 10);
          print(privateKey);
          final des = decryptAESCryptoJS(data, privateKey);
          var map = jsonDecode(des);
          PaymentRes response = PaymentRes.fromJson(map);
          return response;
        } else {
          CustomToast.showToast(data.toString());
          print(data.toString());
          return null;
        }
      } on DioException catch (e) {
        if (e.type == DioExceptionType.badResponse) {
          pd.close();
          if (e.response != null) {
            CustomToast.showToast('Server error: ' + "${e.response!.data}");
            print('Server error: ' + "${e.response!.data}");
          }
        }
        return null;
      }
    } else
      return false;
  }

  checkPaymentInitFunc() {
    OverlayState? state = Overlay.of(context);

    setState(() {
      _paymentStatusInit = checkUserPayment();
    });

    checkUserPayment().then((value) {
      PaymentRes? paymentStatus;
      if (value.runtimeType == PaymentRes) {
        paymentStatus = value;

        var status = paymentStatus?.status ?? false;

        if (paymentStatus != null && !status) {
          WidgetsBinding.instance.addPostFrameCallback((time) {
            showTopSnackBar(
              state,
              CustomSnackBar.error(
                message: paymentStatus?.text ?? 'Loading...',
              ),
            );
          });
        }
      }
      if (value.runtimeType == bool) {
        if (value == false) {
          WidgetsBinding.instance.addPostFrameCallback((time) {
            showTopSnackBar(
              state,
              CustomSnackBar.error(
                message:
                    'Интернет билан алоқа йўқ илтимос алоқани қайта текширинг!',
              ),
            );
          });
        }
      }
    });
  }

  @override
  void initState() {
    checkPaymentInitFunc();

    _bloc = BlocProvider.of<NotSendBloc>(context);
    pd = ProgressDialog(context: context);
    super.initState();
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  // FloatingActionButton(
  // onPressed: () async {
  // if (await networkInfo.isConnected) {
  // // _bloc.add(SetNotSendEvent(
  // //   notSendModel: state.list[index],
  // // ));
  // if (data.isNotEmpty && selectedIndex != -1) {
  // sendData(data[selectedIndex]);
  // } else {
  // debugPrint("Empty data");
  // }
  // } else {
  // CustomToast.showToast(
  // "Интернет билан алоқа ёқ илтимос алоқани қайта текширинг!");
  // }
  // },
  // backgroundColor: cFirstColor,
  // shape: RoundedRectangleBorder(
  // borderRadius: BorderRadius.circular(10.0),
  // ),
  // child: SvgPicture.asset(
  // "assets/icons/send_icon.svg",
  // width: 30.w,
  // height: 30.h,
  // color: cWhiteColor,
  // ))

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      floatingActionButton: AnimatedScale(
        scale: selectedIndex == 0 ? 1 : 0,
        duration: const Duration(milliseconds: 200),
        child: SizedBox(
          width: 120.w,
          child: FutureBuilder<dynamic>(
            future: _paymentStatusInit,
            builder: (context, snapshot) {
              PaymentRes? paymentStatus;
              bool? hasInternet = true;
              if (snapshot.data.runtimeType == PaymentRes) {
                paymentStatus = snapshot.data;
              }
              if (snapshot.data.runtimeType == bool) {
                hasInternet = snapshot.data;
              }

              switch (snapshot.connectionState) {
                case ConnectionState.waiting:
                  {
                    // Otherwise, display a loading indicator.
                    return FloatingActionButton(
                        heroTag: "btn3",
                        onPressed: () {},
                        backgroundColor: cFirstColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Center(
                            child: CupertinoActivityIndicator(
                          color: cWhiteColor,
                        )));
                  }
                default:
                  if (snapshot.hasError)
                    return FloatingActionButton(
                        heroTag: "btn4",
                        onPressed: () {
                          launchCustomUrl();
                        },
                        backgroundColor: cRedColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Text(
                          "Error!",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: cWhiteColor,
                              fontSize: 18.sp,
                              fontFamily: 'Regular'),
                        ));
                  else {
                    // If the Future is complete, display the preview.

                    return FloatingActionButton(
                        heroTag: "btn5",
                        onPressed: paymentStatus != null &&
                                paymentStatus.status == true
                            ? () async {
                                if (await networkInfo.isConnected) {
                                  if (data.isNotEmpty && selectedIndex != -1) {
                                    sendData(data[selectedIndex]);
                                  } else {
                                    debugPrint("Empty data");
                                  }
                                } else {
                                  CustomToast.showToast(
                                      "Интернет билан алоқа йўқ илтимос алоқани қайта текширинг!");
                                }
                              }
                            : paymentStatus == null && hasInternet == true
                                ? () async {
                                    launchCustomUrl();
                                  }
                                : hasInternet == false
                                    ? () async {
                                        checkPaymentInitFunc();
                                      }
                                    : () async {
                                        Navigator.push(
                                          context,
                                          CupertinoPageRoute(
                                              builder: (context) =>
                                                  const PaymentsPage()),
                                        ).then(
                                            (value) => checkPaymentInitFunc());
                                      },
                        backgroundColor: paymentStatus != null &&
                                paymentStatus.status == true
                            ? cFirstColor
                            : cPurpleColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                          child: Text(
                            paymentStatus != null &&
                                    paymentStatus.status == true
                                ? "Юбориш"
                                : paymentStatus == null && hasInternet == true
                                    ? "Error (Admin)"
                                    : hasInternet == false
                                        ? "Янгилаш"
                                        : "Тўлов қилиш",
                            maxLines: 2,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: cWhiteColor,
                                fontSize: 18.sp,
                                fontFamily: 'Regular'),
                          ),
                        ));
                  }
              }
            },
          ),
        ),
      ),
      backgroundColor: cBackColor,
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 20.h),
            height: 124.h,
            decoration: BoxDecoration(
              color: cFirstColor,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(cRadius22.r),
                bottomRight: Radius.circular(cRadius22.r),
              ),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: SvgPicture.asset(
                        "assets/icons/arrow_left.svg",
                        width: 24.w,
                        height: 24.h,
                      ),
                    ),
                  ),
                  Text("Юборилмаганлар",
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      style: TextStyle(
                          fontSize: 18.sp,
                          color: cWhiteColor,
                          fontFamily: 'Medium')),
                  SizedBox(
                    width: 24.w,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 12.h,
          ),
          Expanded(
            child: BlocBuilder<NotSendBloc, NotSendState>(
              builder: (context, state) {
                if (state is NotSendFailure) {
                  CustomToast.showToast(
                      "Маълумотлар юкланишда хатолик юз берди!");
                }
                if (state is NotSendSuccess) {
                  data = state.list;
                  return ListView.separated(
                    separatorBuilder: (context, index) {
                      return Divider();
                    },
                    itemBuilder: (context, index) {
                      return Slidable(
                        key: ValueKey(index),
                        endActionPane: ActionPane(
                          motion: const ScrollMotion(),
                          children: [
                            SlidableAction(
                              onPressed: (context) async {
                                var result = await showAlert(context) ?? false;
                                if (result) {
                                  var deletable = state.list[index];
                                  final box = Hive.box(forSendBox);
                                  box.delete(deletable.key);
                                  _bloc.add(GetNotSendEvent());
                                  CustomToast.showToast("Ўчирилди");
                                  setState(() {
                                    selectedIndex = -1;
                                  });
                                }
                              },
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              icon: Icons.delete,
                              label: 'Ўчириш',
                            ),
                          ],
                        ),
                        child: InkWell(
                          onTap: () {
                            if (selectedIndex == index) {
                              setState(() {
                                selectedIndex = -1;
                              });
                            } else {
                              if (index != 0) {
                                CustomToast.showToast("Юборишни энг биринчисидан бошланг!");
                              }
                              setState(() {
                                selectedIndex = index;
                              });
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: index == selectedIndex ? cFirstColor : cWhiteColor,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 10.w,
                                    top: 10.h,
                                    bottom: 10.h,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.r),
                                  ),
                                  height: 80.h,
                                  width: 80.w,
                                  child: (state.list[index].imagesList![0].image) == null
                                      ? const Placeholder()
                                      : ClipRRect(
                                    borderRadius: BorderRadius.circular(10.r),
                                    child: Image.memory(
                                      base64Decode(state.list[index].imagesList![0].image!),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      margin: EdgeInsets.only(
                                        left: 10.w,
                                        right: 6.w,
                                        top: 10.h,
                                        bottom: 6.h,
                                      ),
                                      width: 250.w,
                                      child: Text(
                                        state.list[index].subCategoryName ?? "...",
                                        style: TextStyle(
                                          color: selectedIndex == index ? cWhiteColor : cBlackColor,
                                          fontSize: 20.sp,
                                          fontFamily: 'SemiBold',
                                        ),
                                        maxLines: 1,
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                        left: 10.w,
                                        right: 16.w,
                                        top: 6.h,
                                        bottom: 5.h,
                                      ),
                                      width: 250.w,
                                      child: Text(
                                        state.list[index].text ?? "...",
                                        style: TextStyle(
                                          color: selectedIndex == index ? cWhiteColor : cBlackColor,
                                          fontSize: 17.sp,
                                          fontFamily: 'Medium',
                                        ),
                                        maxLines: 2,
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                        left: 10.w,
                                        right: 16.w,
                                        top: 6.h,
                                        bottom: 16.h,
                                      ),
                                      width: 250.w,
                                      child: Text(
                                        state.list[index].imagesList![0].sana ?? "...",
                                        style: TextStyle(
                                          color: selectedIndex == index ? cWhiteColor : cBlackColor,
                                          fontSize: 14.sp,
                                          fontFamily: 'Regular',
                                        ),
                                        maxLines: 2,
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    physics: const BouncingScrollPhysics(),
                    itemCount: state.list.length,
                  );
                }
                if (state is NotSendEmpty) {
                  return Container(
                    child: Center(
                      child: Image.asset(
                        Assets.iconsEmpty,
                        height: 300.h,
                      ),
                    ),
                  );
                } else {
                  return Container();
                }
              },
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
        ],
      ),
    );
  }

  void sendData(NotSendModel notSendModel) async {
    pd.show(max: 100, msg: "Маълумотлар юкланмоқда...", msgFontSize: 17.sp);

    var imageList = notSendModel.imagesList!;
    List<MultipartFile> files = [];
    var lat = imageList.last.latLang!.split(",").first;
    var lng = imageList.last.latLang!.split(",").last;
    var now = DateTime.now();
    var time =
        imageList.last.sana ?? (DateFormat('yyyy-MM-dd HH:mm:ss').format(now));

    for (var item in imageList) {
      MultipartFile file = await MultipartFile.fromBytes(
        base64Decode(item.image!),
        filename: item.image!.substring(0, 8),
        contentType: MediaType('image', 'png'),
      );
      files.add(file);
      print(files.length);
    }

    FormData formData = FormData.fromMap({
      'lat': lat,
      'lng': lng,
      'files': files,
      'sound': notSendModel.title,
      'desc': notSendModel.text,
      'province': sharedPreferences.getString("provinceId"),
      'region': sharedPreferences.getString("regionId"),
      'sector': sharedPreferences.getString("sectorId"),
      'user': notSendModel.userId,
      'category': notSendModel.categoryId,
      'subCategory': notSendModel.subCategoryId,
      'onBehalf': {
        'status': notSendModel.orinbosarIshtirokida,
        'reason': notSendModel.cause
      },
      'uploadTime': time,
    });

    try {
      Options options = Options(
        receiveDataWhenStatusError: true,
        headers: {
          "Content-Type": "multipart/form-data",
          "Accept": "application/json",
          "Authorization": "Bearer ${sharedPreferences.getString("token")}"
        },
        receiveTimeout: Duration(milliseconds: 60 * 1000),
        sendTimeout: Duration(milliseconds: 60 * 1000),
      );

      final response = await dio.post(
        baseUrl + worksPath,
        data: formData,
        options: options,
        onSendProgress: (int sent, int total) {
          pd.update(value: (sent / total * 100).round() - 1);
        },
      );
      if (response.statusCode == 200) {
        final box = Hive.box(forSendBox);
        box.delete(notSendModel.key);
        _bloc.add(GetNotSendEvent());
        pd.close();
        CustomToast.showToast("Маълумотлар юкланди!");

        setState(() {
          selectedIndex = -1;
        });
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        pd.close();
        if (e.response != null) {
          CustomToast.showToast("Хатолик: ${e.response?.data['message']}");
        }
        return;
      }
      if (e.type == DioExceptionType.connectionTimeout) {
        pd.close();
        print('check your connection');
        return;
      }

      if (e.type == DioExceptionType.receiveTimeout) {
        pd.close();
        print('unable to connect to the server');
        CustomToast.showToast(
            "Server time out...");
        return;
      }

      if (e.type == DioExceptionType.unknown) {
        pd.close();
        print('Something went wrong');
        return;
      }
      pd.close();
    } catch (e) {
      CustomToast.showToast(
          "Малумотлар юкланишда ҳатолик.. Сервер формати хато!");
      pd.close();
    }
  }
}
