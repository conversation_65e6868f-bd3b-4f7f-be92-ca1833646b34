import 'dart:io';
import 'package:path_provider/path_provider.dart' as path_provider;
import 'package:path/path.dart' as p;
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

import '../../di/dependency_injection.dart';
import '../../features/send_data/presentetion/pages/camera_page.dart';

abstract class ImagePickerUtils {
  Future<String> selectImageFromCamera(BuildContext context);
}

class ImagePickerUtilsImpl extends ImagePickerUtils {
  CameraDescription? camera = di();

  @override
  Future<String> selectImageFromCamera(BuildContext context) async {
    try {
      String image = await Navigator.push(
          context,
          CupertinoPageRoute(
              builder: (context) => TakePictureScreen(camera: camera!)));

      // Create a new path for the compressed image
      final directory = await path_provider.getTemporaryDirectory();
      final fileName = p.basename(image);
      final compressedFilePath = p.join(directory.path, 'compressed_$fileName');

      var result = await FlutterImageCompress.compressAndGetFile(
        image,
        compressedFilePath,
        quality: 30,
      );
      return result?.path ?? '';
    } catch (ex) {
      print("Exception from image_picker_utils.dart:" + ex.toString());
      return '';
    }
  }
}