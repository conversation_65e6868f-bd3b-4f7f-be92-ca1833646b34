import 'dart:async';
import 'dart:io';

import 'package:etahlil/core/utils/app_constants.dart';
import 'package:hive/hive.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class HiveHelper {
  /// Get a name list of existing boxes

  FutureOr<List<String>> getNamesOfBoxes() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      var files = appDir.listSync();
      var _list = <String>[];

      files.forEach((file) {
        if (file.statSync().type == FileSystemEntityType.file &&
            p.extension(file.path).toLowerCase() == '.hive') {
          _list.add(p.basenameWithoutExtension(file.path));
        }
      });
      print('Current boxes: $_list');

      return _list;
    } catch (e) {
      print(e);
      return [];
    }
  }

/* ---------------------------------------------------------------------------- */

  /// Delete existing boxes from disk
  Future deleteBoxes() async {
    Hive.close();

    try {
      final _boxes = await getNamesOfBoxes();
      if (_boxes.isNotEmpty)
        _boxes.forEach((name) {
          Hive.deleteBoxFromDisk(name);
          print("Hive box: $name deleted!");
        });
    } catch (e) {
      print(e);
    }
  }
}

launchCaller() async {
  const url = TEL;
  var uri = Uri(scheme: 'tel', path: url);

  if (await canLaunchUrl(uri)) {
    await launchUrl(uri);
  } else {
    throw 'Could not launch $url';
  }
}

launchCustomUrl() async {
  var url = SUPPORT_TG;
  var uri = Uri(scheme: 'https', path: url);

  if (await canLaunchUrl(uri)) {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } else {
    throw 'Could not launch $url';
  }
}
