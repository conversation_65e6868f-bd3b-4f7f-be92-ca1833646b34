import 'package:flutter/cupertino.dart';

Future<bool?> showAlert(BuildContext context) async {
  return await showCupertinoDialog<bool>(
      context: context,
      builder: (BuildContext ctx) {
        return CupertinoAlertDialog(
          title: const Text('Тасдиқланг'),
          content: const Text('Ростдан ҳам ўчирмоқчимисиз?'),
          actions: [
            // The "Yes" button
            CupertinoDialogAction(
                onPressed: () {
                  // Close the dialog
                  Navigator.pop(ctx, true);
                },
                child: const Text('Ҳа')),
            CupertinoDialogAction(
                onPressed: () {
                  // Close the dialog
                  Navigator.pop(ctx, false);
                },
                child: const Text('Йўқ'))
          ],
        );
      });
}
