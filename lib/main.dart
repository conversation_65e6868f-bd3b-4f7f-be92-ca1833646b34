import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'core/network/network_info.dart';
import 'di/dependency_injection.dart' as di;
import 'features/app.dart';
import 'package:flutter/services.dart';

import 'firebase_options.dart';

void main() async {

  runZonedGuarded(() async {

    ///Don't move or touch stupid
    WidgetsFlutterBinding.ensureInitialized();
    HttpOverrides.global = MyHttpOverrides();
    await di.init();

    await Firebase.initializeApp(
      name: 'e-tahlil',
      options: DefaultFirebaseOptions.currentPlatform,
    );
    FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

    SystemChrome.setPreferredOrientations(
      [
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ],
    ).then((val) {
      runApp(AppProvider());
    });

  }, (error, stacktrace) {
    log('runZonedGuarded Errors: $error');
    FirebaseCrashlytics.instance.recordError(error, stacktrace, fatal: true);
    debugPrint("E-Tahlil app error");
  });
}
