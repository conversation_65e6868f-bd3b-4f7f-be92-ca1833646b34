name: etahlil
description: A new Flutter project.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 3.2.0+29

environment:
  sdk: ">=3.1.0 <=4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  # 1.Connection checker
  internet_connection_checker: ^0.0.1+3
  # 2.Service Locator
  get_it: ^8.0.3
  # 3.State Management
  flutter_bloc: ^9.0.0
  # 4.Network calls
  http: ^1.3.0
  dio: ^5.8.0+1
  # 5.Local cache
  shared_preferences: ^2.0.13
  # 6.Local database
  hive: ^2.0.6
  hive_flutter: ^1.1.0
  # 7.Message viewer
  fluttertoast: ^8.0.8
  # 8.Support svg icons
  flutter_svg: ^2.0.17
  # 9.Support text sizes (Unused)
  auto_size_text: ^3.0.0
  # 10.Responsive ui
  flutter_screenutil: ^5.8.4
  # 11.Formatter
  mask_text_input_formatter: ^2.1.0
  # 12.PinCode helper
  pin_code_fields: ^8.0.1
  # 13.Bloc cached clear helper
  bloc_concurrency: ^0.3.0
  # 14.Image picker from camera
  image_picker: ^1.1.2
  # 15.intro views images
  carousel_slider: ^5.0.0
  # 16. For get current time and others
  intl: ^0.19.0
  # 17.Value equality
  equatable: ^2.0.3
  # 18.Functional programming thingies
  dartz: ^0.10.1
  # 19.1 For android version
  device_info_plus: ^11.3.0
  #For versioning
  package_info_plus: ^8.2.1
  # 19.2 For getting Mac address/Other info
  platform_device_id_plus: ^1.0.7-beta
  # 20.For get current location
  geolocator: ^13.0.2
  # 21.For Image cached
  cached_network_image: ^3.4.0
  # 22.For Progress
  sn_progress_dialog: ^1.1.3
  # 23. For Fingerprint
  local_auth: ^2.3.0
  # 24 For stream (Unused)
  rxdart: ^0.28.0
  # 25 For Slidable items list
  flutter_slidable: ^4.0.0
  # 26 For Recording Voice
  #  social_media_recorder: ^1.2.1
  # 27 For Playing Audio
  kplayer: ^0.4.2
  # 28 JWT Token Decoder
  jwt_decoder: ^2.0.1
  # 29 Smart Select
  flutter_awesome_select_clone: ^7.5.0
  # 30 For Media type
  http_parser: ^4.1.2
  # 31 SMS Autoreader
  auto_sms_verification: ^0.0.8
  # 32 Gallery Saver (Saves Images and Videos even for Android 10/11/12)
  gal: ^2.3.1
  # 33 Opening URL/DIAL/MAILS/FILES
  url_launcher: ^6.1.5
  # 34 Orientation
  native_device_orientation: ^2.0.3
  # 35 Image Modifications
  flutter_image_compress: ^2.4.0
  # 36 Image Modifications
  mno_zoom_widget: ^0.1.0
  # 37 Updater
  in_app_update: ^4.2.3

  # Additional
  permission_handler: ^11.4.0
  external_path: ^2.0.1
  get: ^4.6.5

  # For casual camera (renewed)
  camera: ^0.10.3
  #For getting root path
  path_provider: ^2.0.11
  #Need to show top snack-bar
  top_snackbar_flutter: ^3.0.0+1
  #For web browsing
  flutter_inappwebview: ^6.1.5
  #For encrypt/decryption
  aescryptojs: ^1.0.0
  #For grouped list
  grouped_list: ^6.0.0
  #For dropdown
  dropdown_button2: ^2.3.9
  #For sessions
  flutter_session_manager: ^1.0.3

  # Firebase
  firebase_crashlytics: ^4.3.3
  firebase_core: ^3.12.0

dev_dependencies:
  flutter_test:
    sdk: flutter


  # 0. For Syntax Correction (Not used)
  flutter_lints: ^5.0.0

  # 1.For Splash
  flutter_native_splash: ^2.0.3+1

  # 2.For Local database
  build_runner: ^2.1.7
  hive_generator: ^2.0.1
  flutter_gen_runner: ^5.9.0
  # flutter pub run build_runner build --delete-conflicting-outputs

  #Changing app & package name
  rename: ^3.0.2
  # https://pub:dev/packages/rename

  #Changing app icon
  flutter_launcher_icons: ^0.14.3
  # flutter pub run flutter_launcher_icons:main

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: assets/icons/logo.png
  image_path_ios: assets/icons/logoIOS.png
  min_sdk_android: 21 # android min sdk min:16, default 21

#  dart run flutter_native_splash:create
flutter_native_splash:
  color: '#FFFFFF'
  image: assets/images/logoTahlil.png
  android: true
  ios: true
  info_plist_files:
    - 'ios/Runner/Info-Debug.plist'
    - 'ios/Runner/Info-Release.plist'
  android_12:
    color: "#FFFFFF"
    # The image parameter sets the splash screen icon image.  If this parameter is not specified,
    # the app's launcher icon will be used instead.
    # Please note that the splash screen will be clipped to a circle on the center of the screen.
    # App icon with an icon background: This should be 960×960 pixels, and fit within a circle
    # 640 pixels in diameter.
    # App icon without an icon background: This should be 1152×1152 pixels, and fit within a circle
    # 768 pixels in diameter.
    image: assets/images/logoTahlil.png

    #App icon background color.
    icon_background_color: "#FFFFFF"

    # The branding property allows you to specify an image used as branding in the splash screen.
    #branding: assets/dart.png


flutter:

  uses-material-design: true

  assets:
    - assets/icons/
    - assets/images/
  fonts:
    - family: Medium
      fonts:
        - asset: assets/fonts/Montserrat-Medium.ttf
    - family: Regular
      fonts:
        - asset: assets/fonts/Montserrat-Regular.ttf
    - family: SemiBold
      fonts:
        - asset: assets/fonts/Montserrat-SemiBold.ttf